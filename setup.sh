#!/bin/bash

echo "🚀 Setting up SolQuant TypeScript Project"
echo "========================================"

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    echo "Visit: https://nodejs.org/"
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Install dependencies
echo ""
echo "📦 Installing dependencies..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ Dependencies installed successfully!"
else
    echo "❌ Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo ""
    echo "📝 Creating .env file..."
    cp .env.example .env
    echo "✅ .env file created from .env.example"
    echo ""
    echo "⚠️  IMPORTANT: Please edit .env file and add your private key!"
    echo "   - Get devnet SOL from: https://faucet.solana.com/"
    echo "   - Add your wallet private key to PRIVATE_KEY in .env"
    echo ""
else
    echo "✅ .env file already exists"
fi

# Build the project
echo ""
echo "🔨 Building TypeScript project..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ Project built successfully!"
else
    echo "❌ Failed to build project"
    exit 1
fi

echo ""
echo "🎉 Setup completed successfully!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your private key"
echo "2. Get devnet SOL from: https://faucet.solana.com/"
echo "3. Run the swap: npm run dev"
echo ""
echo "Happy trading! 🚀"
