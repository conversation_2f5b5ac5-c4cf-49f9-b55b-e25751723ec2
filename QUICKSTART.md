# 🚀 Quick Start Guide - TypeScript vs Rust

## 1. 立即体验 TypeScript 版本

```bash
# 1. 安装依赖
npm install

# 2. 创建环境配置
cp .env.example .env

# 3. 运行演示 (无需真实私钥)
npm run demo
```

## 2. 完整设置 (需要钱包)

```bash
# 1. 编辑 .env 文件，添加你的私钥
nano .env

# 2. 获取测试网 SOL
# 访问: https://faucet.solana.com/

# 3. 运行真实交换
npm run dev
```

## 3. 项目结构对比

### TypeScript 版本 (当前目录)
```
├── src/
│   ├── main.ts      # 主程序
│   ├── demo.ts      # 演示程序  
│   ├── config.ts    # 配置管理
│   ├── wallet.ts    # 钱包管理
│   ├── raydium.ts   # Raydium API
│   └── swap.ts      # 交换逻辑
├── package.json     # 依赖管理
└── tsconfig.json    # TypeScript 配置
```

### Rust 版本 (src/ 目录)
```
├── src/
│   ├── main.rs           # 主程序
│   ├── common/config.rs  # 配置管理
│   ├── database/         # 数据库模块
│   ├── raydium/          # Raydium 集成
│   └── strategy/         # 策略模块
├── Cargo.toml           # 依赖管理
└── .config.toml         # 配置文件
```

## 4. 开发体验对比

### TypeScript 优势 ✅
- **快速开始**: 5分钟即可运行
- **丰富生态**: npm 包生态系统
- **调试友好**: 优秀的调试工具
- **JSON 原生**: 完美的 API 集成
- **社区支持**: 大量 DeFi 示例

### Rust 优势 ✅  
- **性能优越**: 更快的执行速度
- **内存安全**: 编译时保证
- **原生集成**: 直接调用 Solana 程序
- **生产就绪**: 适合高频交易
- **类型安全**: 更强的类型系统

## 5. 功能对比

| 功能 | TypeScript | Rust |
|------|------------|------|
| 开发速度 | 🟢 很快 | 🟡 中等 |
| 运行性能 | 🟡 中等 | 🟢 很快 |
| 调试体验 | 🟢 优秀 | 🟡 中等 |
| API 集成 | 🟢 简单 | 🟡 复杂 |
| 错误处理 | 🟡 运行时 | 🟢 编译时 |
| 学习曲线 | 🟢 平缓 | 🔴 陡峭 |

## 6. 使用建议

### 选择 TypeScript 如果:
- 需要快速原型开发
- 主要做策略研究和回测
- 团队熟悉 JavaScript/TypeScript
- 需要频繁调用 Web API
- 要构建 Web 界面

### 选择 Rust 如果:
- 构建生产级高频交易系统
- 需要极致性能
- 要直接与 Solana 程序交互
- 团队有系统编程经验
- 对内存使用有严格要求

## 7. 立即测试

```bash
# 运行 TypeScript 演示
npm run demo

# 查看输出，体验开发差异
```

## 8. 下一步

基于演示结果，你可以决定:

1. **继续 TypeScript**: 快速迭代，丰富功能
2. **切换到 Rust**: 追求性能，生产部署  
3. **混合方案**: TypeScript 做策略，Rust 做执行

选择哪个主要取决于你的具体需求和团队技能！
