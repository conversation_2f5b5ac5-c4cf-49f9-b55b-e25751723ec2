# WSOL <-> GOAT 交换功能

本项目实现了基于Jupiter API的WSOL和GOAT代币交换功能。

## 功能特性

- ✅ 支持WSOL到GOAT的交换
- ✅ 支持GOAT到WSOL的交换  
- ✅ 使用Jupiter聚合器获取最佳价格
- ✅ 支持滑点保护
- ✅ 支持优先费用设置
- ✅ 自动处理SOL包装/解包装
- ✅ 完整的错误处理和日志记录

## 代币信息

- **WSOL (Wrapped SOL)**: `So11111111111111111111111111111111111111112`
- **GOAT**: `B2qpj8HuB6McyKU8M2EicEEVK5Huc9VpM42sDb7zQAAy`

## 使用方法

### 1. 准备工作

确保你有Solana密钥对文件在 `~/.config/solana/` 目录下，并且账户中有足够的SOL用于交易费用。

### 2. 运行交换演示

```bash
# 编译项目
cargo build --release

# 运行交换演示
cargo run -- --swap-demo
```

### 3. 程序化使用

```rust
use solquant::jupiter::swap::{swap_wsol_to_goat, swap_goat_to_wsol};
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::signature::Keypair;

// WSOL -> GOAT
let signature = swap_wsol_to_goat(
    &rpc_client,
    &payer_keypair,
    100_000_000,  // 0.1 SOL
    300,          // 3% 滑点
    true,         // 使用优先费用
).await?;

// GOAT -> WSOL  
let signature = swap_goat_to_wsol(
    &rpc_client,
    &payer_keypair,
    1_000_000,    // 1 GOAT
    300,          // 3% 滑点
    true,         // 使用优先费用
).await?;
```

## API 参考

### 主要函数

#### `swap_wsol_to_goat`
将WSOL交换为GOAT代币。

**参数:**
- `rpc_client: &RpcClient` - Solana RPC客户端
- `payer: &Keypair` - 支付者密钥对
- `wsol_amount: u64` - WSOL数量（以lamports为单位）
- `slippage_bps: u16` - 滑点（基点，300 = 3%）
- `use_priority_fee: bool` - 是否使用优先费用

**返回:** `Result<String, Box<dyn Error>>` - 交易签名

#### `swap_goat_to_wsol`
将GOAT代币交换为WSOL。

**参数:**
- `rpc_client: &RpcClient` - Solana RPC客户端
- `payer: &Keypair` - 支付者密钥对
- `goat_amount: u64` - GOAT数量（以最小单位为单位）
- `slippage_bps: u16` - 滑点（基点，300 = 3%）
- `use_priority_fee: bool` - 是否使用优先费用

**返回:** `Result<String, Box<dyn Error>>` - 交易签名

### Jupiter API 封装

#### `JupiterSwap::get_quote`
获取交换报价。

#### `JupiterSwap::get_swap_transaction`
获取交换交易。

#### `JupiterSwap::execute_swap`
执行完整的交换流程。

## 配置

程序会根据 `config.toml` 中的模式设置：
- `PRODUCT` 模式：使用mainnet端点，执行实际交换
- `DEV` 模式：跳过某些测试，用于开发环境

## 安全注意事项

1. **私钥安全**: 确保私钥文件安全存储，不要提交到版本控制系统
2. **滑点设置**: 根据市场条件合理设置滑点，避免MEV攻击
3. **金额检查**: 交换前检查账户余额，避免交易失败
4. **网络费用**: 确保账户有足够SOL支付交易费用

## 错误处理

程序包含完整的错误处理：
- API调用失败
- 网络连接问题
- 余额不足
- 交易失败
- 签名错误

## 测试

```bash
# 运行所有测试
cargo test

# 运行Jupiter相关测试
cargo test jupiter::swap::tests::test_get_quote -- --nocapture
```

## 依赖项

主要依赖：
- `solana-client` - Solana RPC客户端
- `solana-sdk` - Solana SDK
- `reqwest` - HTTP客户端
- `serde` - 序列化/反序列化
- `base64` - Base64编码
- `bincode` - 二进制序列化

## 许可证

本项目遵循MIT许可证。
