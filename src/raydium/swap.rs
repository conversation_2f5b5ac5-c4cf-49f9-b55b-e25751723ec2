use crate::raydium::util::{get_url, send_get_request};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 交换计算请求参数
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapComputeRequest {
    /// 输入代币地址
    pub input_mint: String,
    /// 输出代币地址
    pub output_mint: String,
    /// 输入金额（以最小单位计算）
    pub amount: String,
    /// 滑点容忍度（百分比，例如 0.5 表示 0.5%）
    pub slippage_bps: u32,
    /// 交换类型：ExactIn 或 ExactOut
    pub swap_type: SwapType,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum SwapType {
    ExactIn,
    ExactOut,
}

/// 交换计算响应
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapComputeResponse {
    pub id: String,
    pub success: bool,
    pub data: SwapComputeData,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapComputeData {
    /// 输入代币信息
    pub input_mint: TokenMintInfo,
    /// 输出代币信息
    pub output_mint: TokenMintInfo,
    /// 输入金额
    pub input_amount: String,
    /// 输出金额
    pub output_amount: String,
    /// 其他金额（用于 ExactOut）
    pub other_amount_threshold: String,
    /// 交换类型
    pub swap_type: SwapType,
    /// 滑点容忍度
    pub slippage_bps: u32,
    /// 价格影响百分比
    pub price_impact_pct: f64,
    /// 路由计划
    pub route_plan: Vec<RoutePlan>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenMintInfo {
    pub chain_id: u32,
    pub address: String,
    pub program_id: String,
    pub logo_uri: String,
    pub symbol: String,
    pub name: String,
    pub decimals: u8,
    pub tags: Vec<String>,
    pub extensions: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RoutePlan {
    /// 交换信息
    pub swap_info: SwapInfo,
    /// 百分比
    pub percent: u32,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapInfo {
    /// AMM ID
    pub amm_id: String,
    /// 标签
    pub label: String,
    /// 输入代币地址
    pub input_mint: String,
    /// 输出代币地址
    pub output_mint: String,
    /// 输入金额
    pub input_amount: String,
    /// 输出金额
    pub output_amount: String,
    /// 手续费金额
    pub fee_amount: String,
    /// 手续费代币地址
    pub fee_mint: String,
}

/// 交换指令请求参数
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapInstructionRequest {
    /// 计算数据（从 compute 接口获取）
    pub compute_unit_price_micro_lamports: Option<u64>,
    /// 交换数据
    pub swap_request: SwapRequest,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapRequest {
    /// 输入代币地址
    pub input_mint: String,
    /// 输出代币地址
    pub output_mint: String,
    /// 输入金额
    pub amount: String,
    /// 滑点容忍度
    pub slippage_bps: u32,
    /// 其他金额阈值
    pub other_amount_threshold: String,
    /// 交换类型
    pub swap_type: SwapType,
    /// 交易版本
    pub tx_version: TxVersion,
    /// 用户钱包地址
    pub wallet: String,
    /// 是否包装 SOL
    pub wrap_unwrap_sol: Option<bool>,
    /// 输入代币账户（可选）
    pub input_account: Option<String>,
    /// 输出代币账户（可选）
    pub output_account: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub enum TxVersion {
    Legacy,
    V0,
}

/// 交换指令响应
#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapInstructionResponse {
    pub id: String,
    pub success: bool,
    pub data: SwapInstructionData,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SwapInstructionData {
    /// 交换指令
    pub swap_instruction: String,
    /// 地址查找表账户
    pub address_lookup_table_accounts: Vec<String>,
    /// 计算单元限制
    pub compute_unit_limit: u64,
    /// 计算单元价格
    pub compute_unit_price_micro_lamports: u64,
    /// 优先费用指令
    pub priority_fee_instructions: PriorityFeeInstructions,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct PriorityFeeInstructions {
    /// 计算预算指令
    pub compute_budget_instructions: Vec<ComputeBudgetInstruction>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ComputeBudgetInstruction {
    /// 指令数据
    pub instruction_data: String,
    /// 程序 ID
    pub program_id: String,
    /// 账户
    pub accounts: Vec<InstructionAccount>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct InstructionAccount {
    /// 公钥
    pub pubkey: String,
    /// 是否签名者
    pub is_signer: bool,
    /// 是否可写
    pub is_writable: bool,
}

/// 计算交换
pub async fn compute_swap(
    input_mint: &str,
    output_mint: &str,
    amount: &str,
    slippage_bps: u32,
    swap_type: SwapType,
) -> Result<SwapComputeResponse, Box<dyn std::error::Error>> {
    let url = format!(
        "{}/compute/swap-base-in?inputMint={}&outputMint={}&amount={}&slippageBps={}",
        get_url(),
        input_mint,
        output_mint,
        amount,
        slippage_bps
    );

    let response = send_get_request(url).await?.json().await?;
    Ok(response)
}

/// 获取交换指令
pub async fn get_swap_instruction(
    request: SwapInstructionRequest,
) -> Result<SwapInstructionResponse, Box<dyn std::error::Error>> {
    let client = reqwest::Client::new();
    let url = format!("{}/compute/swap-base-in", get_url());

    let response = client
        .post(&url)
        .header("Content-Type", "application/json")
        .json(&request)
        .send()
        .await?
        .json()
        .await?;

    Ok(response)
}

/// 常用代币地址常量
pub mod tokens {
    /// SOL (Wrapped SOL)
    pub const SOL: &str = "So11111111111111111111111111111111111111112";
    /// USDC
    pub const USDC: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
    /// USDT
    pub const USDT: &str = "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB";
    /// RAY
    pub const RAY: &str = "4k3Dyjzvzp8eMZWUXbBCjEvwSkkk59S5iCNLY3QrkX6R";
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::common::config::{get, Mode};

    #[tokio::test]
    async fn test_compute_swap() {
        let _ = tracing_subscriber::fmt::try_init();

        // Skip test if not in PRODUCT mode
        let config = get();
        if config.mode != Mode::PRODUCT {
            println!("Skipping test_compute_swap: not in PRODUCT mode");
            return;
        }

        let result = compute_swap(
            tokens::SOL,
            tokens::USDC,
            "1000000000", // 1 SOL (9 decimals)
            50,           // 0.5% slippage
            SwapType::ExactIn,
        )
        .await;

        assert!(result.is_ok());
        let response = result.unwrap();
        assert!(response.success);
        println!("Swap compute result: {:?}", response.data);
    }
}
