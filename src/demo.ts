import { SwapService } from './swap';
import { config, validateConfig, TOKENS } from './config';
import { RaydiumAPI } from './raydium';
import { SolanaWallet } from './wallet';

/**
 * 演示版本 - 展示 TypeScript 在 DeFi 开发中的优势
 */
async function demo() {
  console.log('🎭 SolQuant TypeScript Demo - Raydium Integration');
  console.log('=================================================\n');

  try {
    // 1. 配置验证 - TypeScript 的类型安全
    console.log('1️⃣ Configuration & Type Safety');
    console.log('--------------------------------');
    validateConfig();
    console.log('✅ Configuration validated with TypeScript types');
    console.log(`   RPC: ${config.rpcUrl}`);
    console.log(`   Slippage: ${config.slippageBps / 100}%`);
    console.log(`   Amount: ${config.swapAmount} SOL\n`);

    // 2. API 连接 - 展示 TypeScript 的异步处理
    console.log('2️⃣ API Connections & Async Handling');
    console.log('------------------------------------');
    const raydium = new RaydiumAPI();
    const wallet = new SolanaWallet();
    
    const isConnected = await raydium.checkConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to Raydium API');
    }
    
    await wallet.checkWalletStatus();
    console.log('');

    // 3. 数据获取和处理 - TypeScript 的 JSON 处理优势
    console.log('3️⃣ Data Fetching & JSON Processing');
    console.log('-----------------------------------');
    
    // 获取池信息
    console.log('📊 Fetching SOL/USDC pool information...');
    const poolInfo = await raydium.getPoolInfo(TOKENS.SOL, TOKENS.USDC);
    
    if (poolInfo.data.data.length > 0) {
      const pool = poolInfo.data.data[0];
      console.log('✅ Pool data retrieved and parsed:');
      console.log(`   Pool ID: ${pool.id}`);
      console.log(`   Type: ${pool.type || pool.poolType}`);
      console.log(`   Price: $${pool.price?.toFixed(4) || 'N/A'}`);
      console.log(`   TVL: $${pool.tvl?.toLocaleString() || 'N/A'}`);
      console.log(`   24h Volume: $${pool.day?.volume?.toLocaleString() || 'N/A'}`);
    }
    console.log('');

    // 4. 交换计算 - 展示复杂数据结构处理
    console.log('4️⃣ Swap Calculation & Route Planning');
    console.log('------------------------------------');
    
    const amountInLamports = Math.floor(config.swapAmount * 1e9); // SOL has 9 decimals
    console.log(`💰 Calculating swap for ${amountInLamports} lamports (${config.swapAmount} SOL)`);
    
    const computeResult = await raydium.computeSwap(
      TOKENS.SOL,
      TOKENS.USDC,
      amountInLamports.toString(),
      config.slippageBps
    );

    console.log('✅ Swap computation completed:');
    console.log(`   Input: ${computeResult.data.inputAmount} lamports`);
    console.log(`   Expected Output: ${computeResult.data.outputAmount} (${computeResult.data.outputMint.symbol})`);
    console.log(`   Price Impact: ${computeResult.data.priceImpactPct.toFixed(4)}%`);
    console.log(`   Route Steps: ${computeResult.data.routePlan.length}`);
    
    // 显示路由详情
    computeResult.data.routePlan.forEach((route, index) => {
      console.log(`   Route ${index + 1}: ${route.swapInfo.label} (${route.percent}%)`);
    });
    console.log('');

    // 5. TypeScript 类型系统的优势展示
    console.log('5️⃣ TypeScript Type System Benefits');
    console.log('----------------------------------');
    console.log('✅ Strong typing prevents runtime errors:');
    console.log('   - API responses are typed and validated');
    console.log('   - Configuration is type-safe');
    console.log('   - IDE provides excellent autocomplete');
    console.log('   - Compile-time error checking');
    console.log('');

    // 6. 开发体验对比
    console.log('6️⃣ Development Experience Comparison');
    console.log('------------------------------------');
    console.log('🟢 TypeScript Advantages:');
    console.log('   ✅ Rapid prototyping and iteration');
    console.log('   ✅ Rich ecosystem (npm packages)');
    console.log('   ✅ Excellent debugging tools');
    console.log('   ✅ Native JSON/API handling');
    console.log('   ✅ Large DeFi community');
    console.log('   ✅ Easy integration with web frontends');
    console.log('');
    console.log('🟡 Rust Advantages:');
    console.log('   ✅ Superior performance');
    console.log('   ✅ Memory safety');
    console.log('   ✅ Native Solana integration');
    console.log('   ✅ Better for high-frequency trading');
    console.log('   ✅ Compile-time guarantees');
    console.log('');

    // 7. 实际交换演示（模拟）
    console.log('7️⃣ Transaction Simulation');
    console.log('--------------------------');
    console.log('🎭 Simulating swap transaction...');
    console.log('   (In production, this would execute the real swap)');
    console.log('');
    console.log('📝 Transaction steps would be:');
    console.log('   1. Create swap instruction from Raydium API');
    console.log('   2. Build versioned transaction (V0)');
    console.log('   3. Add compute budget and priority fees');
    console.log('   4. Sign with wallet keypair');
    console.log('   5. Send and confirm transaction');
    console.log('   6. Wait for confirmation');
    console.log('');

    // 8. 总结
    console.log('8️⃣ Summary & Recommendations');
    console.log('-----------------------------');
    console.log('🎯 For your use case, TypeScript offers:');
    console.log('   ✅ Much faster development cycle');
    console.log('   ✅ Easier debugging and testing');
    console.log('   ✅ Better API integration');
    console.log('   ✅ Simpler JSON data handling');
    console.log('   ✅ Rich DeFi library ecosystem');
    console.log('');
    console.log('💡 Recommendation:');
    console.log('   - Use TypeScript for rapid prototyping and strategy development');
    console.log('   - Consider Rust for production high-frequency systems');
    console.log('   - TypeScript is perfect for most DeFi applications');
    console.log('');

    console.log('🎉 Demo completed successfully!');
    console.log('');
    console.log('Next steps to implement real swapping:');
    console.log('1. Implement proper transaction instruction parsing');
    console.log('2. Handle address lookup tables for V0 transactions');
    console.log('3. Add proper error handling and retry logic');
    console.log('4. Implement slippage protection');
    console.log('5. Add transaction monitoring and confirmation');

  } catch (error) {
    console.error('\n❌ Demo error:', error);
    
    if (error instanceof Error) {
      console.error('Error details:', error.message);
    }
    
    console.log('\n💡 Common issues and solutions:');
    console.log('1. Network issues: Check your internet connection');
    console.log('2. API errors: Verify Raydium API is accessible');
    console.log('3. Wallet issues: Ensure private key is correct');
    console.log('4. Balance issues: Get devnet SOL from faucet');
  }
}

// 运行演示
if (require.main === module) {
  demo();
}

export { demo };
