use base64::Engine;
use reqwest::Client;
use serde::{Deserialize, Serialize};
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::{
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    transaction::VersionedTransaction,
};
use std::error::Error;
use tracing::{error, info};

const JUPITER_QUOTE_API: &str = "https://quote-api.jup.ag/v6";

#[derive(Debug, Serialize, Deserialize)]
pub struct QuoteRequest {
    #[serde(rename = "inputMint")]
    pub input_mint: String,
    #[serde(rename = "outputMint")]
    pub output_mint: String,
    pub amount: u64,
    #[serde(rename = "slippageBps")]
    pub slippage_bps: u16,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QuoteResponse {
    #[serde(rename = "inputMint")]
    pub input_mint: String,
    #[serde(rename = "inAmount")]
    pub in_amount: String,
    #[serde(rename = "outputMint")]
    pub output_mint: String,
    #[serde(rename = "outAmount")]
    pub out_amount: String,
    #[serde(rename = "otherAmountThreshold")]
    pub other_amount_threshold: String,
    #[serde(rename = "swapMode")]
    pub swap_mode: String,
    #[serde(rename = "slippageBps")]
    pub slippage_bps: u16,
    #[serde(rename = "platformFee")]
    pub platform_fee: Option<PlatformFee>,
    #[serde(rename = "priceImpactPct")]
    pub price_impact_pct: String,
    #[serde(rename = "routePlan")]
    pub route_plan: Vec<RoutePlan>,
    #[serde(rename = "contextSlot")]
    pub context_slot: u64,
    #[serde(rename = "timeTaken")]
    pub time_taken: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PlatformFee {
    pub amount: String,
    #[serde(rename = "feeBps")]
    pub fee_bps: u16,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RoutePlan {
    #[serde(rename = "swapInfo")]
    pub swap_info: SwapInfo,
    pub percent: u8,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SwapInfo {
    #[serde(rename = "ammKey")]
    pub amm_key: String,
    pub label: String,
    #[serde(rename = "inputMint")]
    pub input_mint: String,
    #[serde(rename = "outputMint")]
    pub output_mint: String,
    #[serde(rename = "inAmount")]
    pub in_amount: String,
    #[serde(rename = "outAmount")]
    pub out_amount: String,
    #[serde(rename = "feeAmount")]
    pub fee_amount: String,
    #[serde(rename = "feeMint")]
    pub fee_mint: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SwapRequest {
    #[serde(rename = "quoteResponse")]
    pub quote_response: QuoteResponse,
    #[serde(rename = "userPublicKey")]
    pub user_public_key: String,
    #[serde(rename = "wrapAndUnwrapSol")]
    pub wrap_and_unwrap_sol: bool,
    #[serde(rename = "dynamicComputeUnitLimit")]
    pub dynamic_compute_unit_limit: bool,
    #[serde(rename = "prioritizationFeeLamports")]
    pub prioritization_fee_lamports: Option<PrioritizationFee>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PrioritizationFee {
    #[serde(rename = "priorityLevelWithMaxLamports")]
    pub priority_level_with_max_lamports: PriorityLevelWithMaxLamports,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PriorityLevelWithMaxLamports {
    #[serde(rename = "maxLamports")]
    pub max_lamports: u64,
    #[serde(rename = "priorityLevel")]
    pub priority_level: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SwapResponse {
    #[serde(rename = "swapTransaction")]
    pub swap_transaction: String,
    #[serde(rename = "lastValidBlockHeight")]
    pub last_valid_block_height: u64,
    #[serde(rename = "prioritizationFeeLamports")]
    pub prioritization_fee_lamports: u64,
    #[serde(rename = "computeUnitLimit")]
    pub compute_unit_limit: u64,
}

pub struct JupiterSwap {
    client: Client,
}

impl JupiterSwap {
    pub fn new() -> Self {
        Self {
            client: Client::new(),
        }
    }

    /// 获取交换报价
    pub async fn get_quote(
        &self,
        input_mint: &str,
        output_mint: &str,
        amount: u64,
        slippage_bps: u16,
    ) -> Result<QuoteResponse, Box<dyn Error>> {
        let url = format!(
            "{}/quote?inputMint={}&outputMint={}&amount={}&slippageBps={}",
            JUPITER_QUOTE_API, input_mint, output_mint, amount, slippage_bps
        );

        info!("Getting quote from Jupiter: {}", url);

        let response = self.client.get(&url).send().await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Jupiter quote API error: {}", error_text);
            return Err(format!("Jupiter API error: {}", error_text).into());
        }

        let quote: QuoteResponse = response.json().await?;
        info!(
            "Got quote: {} {} -> {} {}",
            quote.in_amount, input_mint, quote.out_amount, output_mint
        );

        Ok(quote)
    }

    /// 获取交换交易
    pub async fn get_swap_transaction(
        &self,
        quote_response: QuoteResponse,
        user_public_key: &Pubkey,
        use_priority_fee: bool,
    ) -> Result<SwapResponse, Box<dyn Error>> {
        let swap_request = SwapRequest {
            quote_response,
            user_public_key: user_public_key.to_string(),
            wrap_and_unwrap_sol: true,
            dynamic_compute_unit_limit: true,
            prioritization_fee_lamports: if use_priority_fee {
                Some(PrioritizationFee {
                    priority_level_with_max_lamports: PriorityLevelWithMaxLamports {
                        max_lamports: 10_000_000, // 0.01 SOL max
                        priority_level: "high".to_string(),
                    },
                })
            } else {
                None
            },
        };

        let url = format!("{}/swap", JUPITER_QUOTE_API);
        info!("Getting swap transaction from Jupiter");

        let response = self
            .client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&swap_request)
            .send()
            .await?;

        if !response.status().is_success() {
            let error_text = response.text().await?;
            error!("Jupiter swap API error: {}", error_text);
            return Err(format!("Jupiter API error: {}", error_text).into());
        }

        let swap_response: SwapResponse = response.json().await?;
        info!(
            "Got swap transaction, compute units: {}",
            swap_response.compute_unit_limit
        );

        Ok(swap_response)
    }

    /// 执行交换
    pub async fn execute_swap(
        &self,
        rpc_client: &RpcClient,
        payer: &Keypair,
        input_mint: &str,
        output_mint: &str,
        amount: u64,
        slippage_bps: u16,
        use_priority_fee: bool,
    ) -> Result<String, Box<dyn Error>> {
        info!(
            "Starting swap: {} {} -> {}",
            amount, input_mint, output_mint
        );

        // 1. 获取报价
        let quote = self
            .get_quote(input_mint, output_mint, amount, slippage_bps)
            .await?;

        // 2. 获取交换交易
        let swap_response = self
            .get_swap_transaction(quote, &payer.pubkey(), use_priority_fee)
            .await?;

        // 3. 反序列化交易
        let swap_transaction_buf =
            base64::engine::general_purpose::STANDARD.decode(&swap_response.swap_transaction)?;
        let mut transaction = bincode::deserialize::<VersionedTransaction>(&swap_transaction_buf)?;

        info!("Transaction deserialized, signing...");

        // 4. 签名交易
        let recent_blockhash = rpc_client.get_latest_blockhash().await?;

        // 为VersionedTransaction签名需要使用不同的方法
        let message = &transaction.message;
        let signers = vec![payer];
        let signatures = signers
            .iter()
            .map(|s| s.sign_message(&message.serialize()))
            .collect::<Vec<_>>();

        transaction.signatures = signatures;

        // 5. 发送交易
        info!("Sending transaction...");
        let signature = rpc_client
            .send_and_confirm_transaction(&transaction)
            .await?;

        let signature_str = signature.to_string();
        info!(
            "Swap completed! Transaction: https://solscan.io/tx/{}",
            signature_str
        );

        Ok(signature_str)
    }
}

/// WSOL和其他代币的mint地址常量
pub const WSOL_MINT: &str = "So11111111111111111111111111111111111111112";
pub const USDC_MINT: &str = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
pub const GOAT_MINT: &str = "B2qpj8HuB6McyKU8M2EicEEVK5Huc9VpM42sDb7zQAAy";

/// 便捷函数：WSOL换GOAT
pub async fn swap_wsol_to_goat(
    rpc_client: &RpcClient,
    payer: &Keypair,
    wsol_amount: u64,
    slippage_bps: u16,
    use_priority_fee: bool,
) -> Result<String, Box<dyn Error>> {
    let jupiter = JupiterSwap::new();
    jupiter
        .execute_swap(
            rpc_client,
            payer,
            WSOL_MINT,
            GOAT_MINT,
            wsol_amount,
            slippage_bps,
            use_priority_fee,
        )
        .await
}

/// 便捷函数：GOAT换WSOL
pub async fn swap_goat_to_wsol(
    rpc_client: &RpcClient,
    payer: &Keypair,
    goat_amount: u64,
    slippage_bps: u16,
    use_priority_fee: bool,
) -> Result<String, Box<dyn Error>> {
    let jupiter = JupiterSwap::new();
    jupiter
        .execute_swap(
            rpc_client,
            payer,
            GOAT_MINT,
            WSOL_MINT,
            goat_amount,
            slippage_bps,
            use_priority_fee,
        )
        .await
}

/// 便捷函数：WSOL换USDC
pub async fn swap_wsol_to_usdc(
    rpc_client: &RpcClient,
    payer: &Keypair,
    wsol_amount: u64,
    slippage_bps: u16,
    use_priority_fee: bool,
) -> Result<String, Box<dyn Error>> {
    let jupiter = JupiterSwap::new();
    jupiter
        .execute_swap(
            rpc_client,
            payer,
            WSOL_MINT,
            USDC_MINT,
            wsol_amount,
            slippage_bps,
            use_priority_fee,
        )
        .await
}

/// 便捷函数：USDC换WSOL
pub async fn swap_usdc_to_wsol(
    rpc_client: &RpcClient,
    payer: &Keypair,
    usdc_amount: u64,
    slippage_bps: u16,
    use_priority_fee: bool,
) -> Result<String, Box<dyn Error>> {
    let jupiter = JupiterSwap::new();
    jupiter
        .execute_swap(
            rpc_client,
            payer,
            USDC_MINT,
            WSOL_MINT,
            usdc_amount,
            slippage_bps,
            use_priority_fee,
        )
        .await
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::common::config::{Mode, get};

    #[tokio::test]
    async fn test_get_quote() {
        let _ = tracing_subscriber::fmt::try_init();

        // Skip test if not in PRODUCT mode
        let config = get();
        if config.mode != Mode::PRODUCT {
            println!("Skipping test_get_quote: not in PRODUCT mode");
            return;
        }

        let jupiter = JupiterSwap::new();
        let result = jupiter
            .get_quote(
                WSOL_MINT,
                GOAT_MINT,
                100_000_000, // 0.1 SOL
                300,         // 3% slippage
            )
            .await;

        match result {
            Ok(quote) => {
                println!("Quote successful:");
                println!("  Input: {} WSOL", quote.in_amount);
                println!("  Output: {} GOAT", quote.out_amount);
                println!("  Price impact: {}%", quote.price_impact_pct);
            }
            Err(e) => {
                println!("Quote failed: {}", e);
                // 在测试环境中，API调用失败是可以接受的
            }
        }
    }
}
