mod common;
mod database;
mod helius;
mod raydium;
mod strategy;
use crate::common::config::load_config;
use crate::helius::price::get_sol_price_in_usd;
use serde_json::Value;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::native_token::LAMPORTS_PER_SOL;
use solana_sdk::signature::Keypair;
use solana_sdk::signature::Signer;
use solana_sdk::signature::read_keypair_file;
use solana_sdk::transaction::Transaction;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use solana_system_interface::instruction as system_instruction;
use spl_associated_token_account::get_associated_token_address;
use spl_token;
use std::error::Error;
use std::fs;
use std::str::FromStr;
use tracing::{error, info};

fn get_local_keypairs() -> Vec<Keypair> {
    let mut keys = Vec::new();
    let home = std::env::var("HOME").unwrap();
    let solana_dir = format!("{}/.config/solana", home);
    let entries = fs::read_dir(&solana_dir).unwrap();

    for entry in entries {
        let entry = entry.unwrap();
        let path = entry.path();
        if path.extension().map(|s| s == "json").unwrap_or(false) {
            if let Ok(keypair) = read_keypair_file(&path) {
                keys.push(keypair);
            }
        }
    }
    keys
}

#[allow(unused)]
async fn process_jupiter_msg(data: Value) -> Result<(), Box<dyn Error>> {
    if let Some(method) = data.get("method").and_then(|m| m.as_str()) {
        info!("method is {}", method);
        if method == "accountNotification" {
            if let Some(params) = data.get("params") {
                let result = &params["result"];
                let value = &result["value"];

                let lamports = value["lamports"].as_u64().unwrap_or(0);
                let owner = value["owner"].as_str().unwrap_or_default();

                info!("账户余额: {}", lamports);
                info!("账户拥有者: {}", owner);
            }
        }
    }

    Ok(())
}

async fn ensure_account_exist(
    key: &Keypair,
    account: &Pubkey,
    token_mint_address: &Pubkey,
) -> Result<(), Box<dyn std::error::Error>> {
    let config = common::config::get();
    let client =
        RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());
    if let Err(_) = client.get_account(account).await {
        let create_ata_ix =
            spl_associated_token_account::instruction::create_associated_token_account(
                &key.pubkey(),
                &key.pubkey(),
                token_mint_address,
                &spl_token::id(),
            );

        let mut transaction = Transaction::new_with_payer(&[create_ata_ix], Some(&key.pubkey()));
        let recent_blockhash = client.get_latest_blockhash().await?;
        transaction.sign(&[key], recent_blockhash);
        client.send_and_confirm_transaction(&transaction).await?;
    }
    Ok(())
}

#[tokio::main(flavor = "multi_thread")]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    let config_path = ".config.toml";
    let config = load_config(config_path)?;
    info!("config is {:#?}", config);

    // Initialize database
    let db_path = config.db_path.clone();
    if let Err(e) = database::common::init_db_with_cfs(&db_path, &[strategy::raydium::CF_NAME]) {
        error!("Failed to initialize database: {}", e);
        return Err(e.into());
    } else {
        info!("Database initialized at: {}", db_path);
    }
    let client =
        RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());

    let price = get_sol_price_in_usd().await?;
    let keys = get_local_keypairs();

    let wsol_mint = Pubkey::from_str("So11111111111111111111111111111111111111112").unwrap();
    let goat_mint = Pubkey::from_str("B2qpj8HuB6McyKU8M2EicEEVK5Huc9VpM42sDb7zQAAy").unwrap();

    let mut key1 = None;
    let mut key2 = None;
    for key in keys {
        info!("");
        info!("");
        let pubkey = key.pubkey();
        match client.get_account(&pubkey).await {
            Ok(account) => {
                let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
                let usd_value = sol_balance * price;
                info!(
                    "[{}]Balance: {} SOL, ${:.2}",
                    pubkey, sol_balance, usd_value
                );
            }
            Err(e) => {
                info!("Get account failed: {}", e);
            }
        }
        if config.mode == common::config::Mode::DEV {
            // deal with wsol
            let wsol_account = get_associated_token_address(&pubkey, &wsol_mint);
            ensure_account_exist(&key, &wsol_account, &wsol_mint).await?;
            let account = client.get_account(&wsol_account).await?;
            let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
            let usd_value = sol_balance * price;
            info!(
                "WSOL[{}]{} SOL, ${:.2}",
                wsol_account, sol_balance, usd_value
            );
            // then try transfer some sol to wsol
            let transfer_value = (0.1 * LAMPORTS_PER_SOL as f64) as u64;
            let transfer_ix = system_instruction::transfer(&pubkey, &wsol_account, transfer_value);
            let sync_native_ix =
                spl_token::instruction::sync_native(&spl_token::id(), &wsol_account)?;
            let mut transaction =
                Transaction::new_with_payer(&[transfer_ix, sync_native_ix], Some(&pubkey));

            let recent_blockhash = client.get_latest_blockhash().await?;
            transaction.sign(&[&key], recent_blockhash);
            client.send_and_confirm_transaction(&transaction).await?;
            info!("Transfer 0.1 SOL >>>>>>>>>>>>>>>>>>>>> WSOL");
            // then check the acount
            if let Ok(account) = client.get_account(&wsol_account).await {
                let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
                let usd_value = sol_balance * price;
                info!(
                    "WSOL[{}]{} SOL, ${:.2}",
                    wsol_account, sol_balance, usd_value
                );
            }
            if let Ok(account) = client.get_token_account_balance(&wsol_account).await {
                let sol_balance = account.ui_amount.unwrap_or(0.0);

                let usd_value = sol_balance * price;
                info!(
                    "WSOL[{}]{} WSOL, ${:.2}",
                    wsol_account, sol_balance, usd_value
                );
            }

            let goat_account = get_associated_token_address(&pubkey, &goat_mint);
            ensure_account_exist(&key, &goat_account, &goat_mint).await?;
            let account = client.get_account(&goat_account).await?;
            let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
            let usd_value = sol_balance * price;
            info!(
                "GOAT[{}]{} SOL, ${:.2}",
                goat_account, sol_balance, usd_value
            );
            // then transfer 0.1 sol to goat account
            let transfer_value = (0.1 * LAMPORTS_PER_SOL as f64) as u64;
            let transfer_ix = system_instruction::transfer(&pubkey, &goat_account, transfer_value);
            let mut transaction = Transaction::new_with_payer(&[transfer_ix], Some(&pubkey));
            let recent_blockhash = client.get_latest_blockhash().await?;
            transaction.sign(&[&key], recent_blockhash);
            client.send_and_confirm_transaction(&transaction).await?;
        }
        if key1.is_none() {
            key1 = Some(key);
        } else if key2.is_none() {
            key2 = Some(key);
        }
    }
    // try to transfer some wsol from key1 to key2
    if let (Some(key1), Some(key2)) = (key1, key2) {
        let wsol_account1 = get_associated_token_address(&key1.pubkey(), &wsol_mint);
        let wsol_account2 = get_associated_token_address(&key2.pubkey(), &wsol_mint);
        let transfer_value = (0.1 * LAMPORTS_PER_SOL as f64) as u64;
        let transfer_ix = spl_token::instruction::transfer(
            &spl_token::id(),
            &wsol_account1,
            &wsol_account2,
            &key1.pubkey(),
            &[],
            transfer_value,
        )?;
        let mut transaction = Transaction::new_with_payer(&[transfer_ix], Some(&key1.pubkey()));
        let recent_blockhash = client.get_latest_blockhash().await?;
        transaction.sign(&[&key1], recent_blockhash);
        client.send_and_confirm_transaction(&transaction).await?;
        // check the sol balance and wsol balance
        let account = client.get_account(&wsol_account1).await?;
        let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
        let usd_value = sol_balance * price;
        info!("[{}]{} SOL, ${:.2}", wsol_account1, sol_balance, usd_value);
        if let Ok(account) = client.get_token_account_balance(&wsol_account1).await {
            let sol_balance = account.ui_amount.unwrap_or(0.0);
            let usd_value = sol_balance * price;
            info!(
                "WSOL[{}]{} WSOL, ${:.2}",
                wsol_account1, sol_balance, usd_value
            );
        }
        let account = client.get_account(&wsol_account2).await?;
        let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
        let usd_value = sol_balance * price;
        info!("[{}]{} SOL, ${:.2}", wsol_account2, sol_balance, usd_value);
        if let Ok(account) = client.get_token_account_balance(&wsol_account2).await {
            let sol_balance = account.ui_amount.unwrap_or(0.0);
            let usd_value = sol_balance * price;
            info!(
                "WSOL[{}]{} WSOL, ${:.2}",
                wsol_account2, sol_balance, usd_value
            );
        }
    }
    // strategy::raydium::run_triangular_arbitrage_task().await;

    Ok(())
}
