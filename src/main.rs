mod common;
mod database;
mod helius;
mod jupiter;
mod raydium;
mod strategy;
use crate::common::config::load_config;
use crate::helius::price::get_sol_price_in_usd;
use serde_json::Value;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_program::program_pack::Pack;
use solana_sdk::signature::Keypair;
use solana_sdk::signature::Signer;
use solana_sdk::signature::read_keypair_file;
use solana_sdk::transaction::Transaction;
use solana_sdk::{commitment_config::CommitmentConfig, pubkey::Pubkey};
use spl_associated_token_account::get_associated_token_address;
use spl_token;
use std::error::Error;
use std::fs;
use std::str::FromStr;
use tracing::{error, info};

fn get_local_keypairs() -> Vec<Keypair> {
    let mut keys = Vec::new();
    let home = std::env::var("HOME").unwrap();
    let solana_dir = format!("{}/.config/solana", home);
    let entries = fs::read_dir(&solana_dir).unwrap();

    for entry in entries {
        let entry = entry.unwrap();
        let path = entry.path();
        if path.extension().map(|s| s == "json").unwrap_or(false) {
            if let Ok(keypair) = read_keypair_file(&path) {
                keys.push(keypair);
            }
        }
    }
    keys
}

#[allow(unused)]
async fn process_jupiter_msg(data: Value) -> Result<(), Box<dyn Error>> {
    if let Some(method) = data.get("method").and_then(|m| m.as_str()) {
        info!("method is {}", method);
        if method == "accountNotification" {
            if let Some(params) = data.get("params") {
                let result = &params["result"];
                let value = &result["value"];

                let lamports = value["lamports"].as_u64().unwrap_or(0);
                let owner = value["owner"].as_str().unwrap_or_default();

                info!("账户余额: {}", lamports);
                info!("账户拥有者: {}", owner);
            }
        }
    }

    Ok(())
}

async fn ensure_account_exist(
    key: &Keypair,
    account: &Pubkey,
    token_mint_address: &Pubkey,
) -> Result<(), Box<dyn std::error::Error>> {
    let config = common::config::get();
    let client =
        RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());
    if let Err(_) = client.get_account(account).await {
        let create_ata_ix =
            spl_associated_token_account::instruction::create_associated_token_account(
                &key.pubkey(),
                &key.pubkey(),
                token_mint_address,
                &spl_token::id(),
            );

        let mut transaction = Transaction::new_with_payer(&[create_ata_ix], Some(&key.pubkey()));
        let recent_blockhash = client.get_latest_blockhash().await?;
        transaction.sign(&[key], recent_blockhash);
        client.send_and_confirm_transaction(&transaction).await?;
    }
    Ok(())
}

/// 演示WSOL和其他代币互换的函数
async fn demo_wsol_token_swap(
    client: &RpcClient,
    payer: &Keypair,
) -> Result<(), Box<dyn std::error::Error>> {
    use crate::jupiter::swap::{
        GOAT_MINT, USDC_MINT, WSOL_MINT, swap_goat_to_wsol, swap_usdc_to_wsol, swap_wsol_to_goat,
        swap_wsol_to_usdc,
    };
    use spl_token::state::Account as TokenAccount;

    info!("=== WSOL <-> 代币 交换演示 ===");

    // 获取用户的WSOL、GOAT和USDC账户地址
    let wsol_mint = Pubkey::from_str(WSOL_MINT)?;
    let goat_mint = Pubkey::from_str(GOAT_MINT)?;
    let usdc_mint = Pubkey::from_str(USDC_MINT)?;
    let user_wsol_account = get_associated_token_address(&payer.pubkey(), &wsol_mint);
    let user_goat_account = get_associated_token_address(&payer.pubkey(), &goat_mint);
    let user_usdc_account = get_associated_token_address(&payer.pubkey(), &usdc_mint);

    // 确保代币账户存在
    ensure_account_exist(payer, &user_goat_account, &goat_mint).await?;
    ensure_account_exist(payer, &user_usdc_account, &usdc_mint).await?;

    // 检查当前余额
    let wsol_balance = match client.get_account(&user_wsol_account).await {
        Ok(account) => {
            let token_account = TokenAccount::unpack(&account.data)?;
            token_account.amount
        }
        Err(_) => 0,
    };

    let goat_balance = match client.get_account(&user_goat_account).await {
        Ok(account) => {
            let token_account = TokenAccount::unpack(&account.data)?;
            token_account.amount
        }
        Err(_) => 0,
    };

    let usdc_balance = match client.get_account(&user_usdc_account).await {
        Ok(account) => {
            let token_account = TokenAccount::unpack(&account.data)?;
            token_account.amount
        }
        Err(_) => 0,
    };

    info!("当前余额:");
    info!(
        "  WSOL: {} ({} SOL)",
        wsol_balance,
        wsol_balance as f64 / 1e9
    );
    info!(
        "  GOAT: {} ({} GOAT)",
        goat_balance,
        goat_balance as f64 / 1e6
    );
    info!(
        "  USDC: {} ({} USDC)",
        usdc_balance,
        usdc_balance as f64 / 1e6
    );

    // 如果有WSOL余额，演示WSOL -> USDC
    if wsol_balance > 0 {
        let swap_amount = std::cmp::min(wsol_balance / 10, 100_000_000); // 最多0.1 SOL或余额的1/10
        if swap_amount > 0 {
            info!(
                "执行 WSOL -> USDC 交换，数量: {} ({} SOL)",
                swap_amount,
                swap_amount as f64 / 1e9
            );

            match swap_wsol_to_usdc(
                client,
                payer,
                swap_amount,
                300,  // 3% 滑点
                true, // 使用优先费用
            )
            .await
            {
                Ok(signature) => {
                    info!("WSOL -> USDC 交换成功! 交易签名: {}", signature);
                }
                Err(e) => {
                    error!("WSOL -> USDC 交换失败: {}", e);
                }
            }
        }
    }

    // 等待一段时间后检查新的USDC余额
    tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

    let new_usdc_balance = match client.get_account(&user_usdc_account).await {
        Ok(account) => {
            let token_account = TokenAccount::unpack(&account.data)?;
            token_account.amount
        }
        Err(_) => 0,
    };

    info!(
        "交换后 USDC 余额: {} ({} USDC)",
        new_usdc_balance,
        new_usdc_balance as f64 / 1e6
    );

    // 如果有USDC余额，演示USDC -> WSOL
    if new_usdc_balance > 0 {
        let swap_amount = std::cmp::min(new_usdc_balance / 2, 10_000_000); // 最多10 USDC或余额的1/2
        if swap_amount > 0 {
            info!(
                "执行 USDC -> WSOL 交换，数量: {} ({} USDC)",
                swap_amount,
                swap_amount as f64 / 1e6
            );

            match swap_usdc_to_wsol(
                client,
                payer,
                swap_amount,
                300,  // 3% 滑点
                true, // 使用优先费用
            )
            .await
            {
                Ok(signature) => {
                    info!("USDC -> WSOL 交换成功! 交易签名: {}", signature);
                }
                Err(e) => {
                    error!("USDC -> WSOL 交换失败: {}", e);
                }
            }
        }
    }

    info!("=== 交换演示完成 ===");
    Ok(())
}

#[tokio::main(flavor = "multi_thread")]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    let config_path = ".config.toml";
    let config = load_config(config_path)?;
    info!("config is {:#?}", config);

    // Initialize database
    let db_path = config.db_path.clone();
    if let Err(e) = database::common::init_db_with_cfs(&db_path, &[strategy::raydium::CF_NAME]) {
        error!("Failed to initialize database: {}", e);
        return Err(e.into());
    } else {
        info!("Database initialized at: {}", db_path);
    }
    let client =
        RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());

    let price = get_sol_price_in_usd().await?;
    let keys = get_local_keypairs();

    info!("SOL价格: ${:.2}", price);
    info!("找到 {} 个本地密钥对", keys.len());

    // 检查命令行参数
    let args: Vec<String> = std::env::args().collect();
    let run_swap_demo = args.len() > 1 && args[1] == "--swap-demo";

    if run_swap_demo {
        info!("运行交换演示模式");
        // 如果有密钥对，演示WSOL和GOAT的交换
        if !keys.is_empty() {
            let payer = &keys[0];
            info!("使用密钥对: {}", payer.pubkey());

            // 演示WSOL和其他代币互换
            if let Err(e) = demo_wsol_token_swap(&client, payer).await {
                error!("交换演示失败: {}", e);
            }
        } else {
            info!("没有找到本地密钥对，跳过交换演示");
            info!("请确保在 ~/.config/solana/ 目录下有密钥对文件");
        }
    } else {
        info!("使用 --swap-demo 参数来运行交换演示");
    }

    // 可选：运行三角套利任务
    // strategy::raydium::run_triangular_arbitrage_task().await;

    Ok(())
}
