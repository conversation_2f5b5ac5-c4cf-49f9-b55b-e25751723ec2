export interface TokenInfo {
  chainId: number;
  address: string;
  programId: string;
  logoURI: string;
  symbol: string;
  name: string;
  decimals: number;
  tags: string[];
  extensions: Record<string, any>;
}

export interface SwapComputeResponse {
  id: string;
  success: boolean;
  data: SwapComputeData;
}

export interface SwapComputeData {
  inputMint: TokenInfo;
  outputMint: TokenInfo;
  inputAmount: string;
  outputAmount: string;
  otherAmountThreshold: string;
  swapType: 'ExactIn' | 'ExactOut';
  slippageBps: number;
  priceImpactPct: number;
  routePlan: RoutePlan[];
}

export interface RoutePlan {
  swapInfo: SwapInfo;
  percent: number;
}

export interface SwapInfo {
  ammId: string;
  label: string;
  inputMint: string;
  outputMint: string;
  inputAmount: string;
  outputAmount: string;
  feeAmount: string;
  feeMint: string;
}

export interface SwapInstructionRequest {
  computeUnitPriceMicroLamports?: number;
  swapRequest: SwapRequest;
}

export interface SwapRequest {
  inputMint: string;
  outputMint: string;
  amount: string;
  slippageBps: number;
  otherAmountThreshold: string;
  swapType: 'ExactIn' | 'ExactOut';
  txVersion: 'LEGACY' | 'V0';
  wallet: string;
  wrapUnwrapSOL?: boolean;
  inputAccount?: string;
  outputAccount?: string;
}

export interface SwapInstructionResponse {
  id: string;
  success: boolean;
  data: SwapInstructionData;
}

export interface SwapInstructionData {
  swapInstruction: string;
  addressLookupTableAccounts: string[];
  computeUnitLimit: number;
  computeUnitPriceMicroLamports: number;
  priorityFeeInstructions: PriorityFeeInstructions;
}

export interface PriorityFeeInstructions {
  computeBudgetInstructions: ComputeBudgetInstruction[];
}

export interface ComputeBudgetInstruction {
  instructionData: string;
  programId: string;
  accounts: InstructionAccount[];
}

export interface InstructionAccount {
  pubkey: string;
  isSigner: boolean;
  isWritable: boolean;
}
