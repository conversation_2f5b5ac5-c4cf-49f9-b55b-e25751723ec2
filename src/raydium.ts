import { config } from './config';
import {
  SwapComputeResponse,
  SwapInstructionRequest,
  SwapInstructionResponse,
  SwapRequest,
} from './types';

export class RaydiumAPI {
  private baseUrl: string;

  constructor() {
    this.baseUrl = config.raydiumApiUrl;
  }

  /**
   * 检查 API 连接
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/main/version`);
      const data = await response.json();
      console.log(`✅ Raydium API connected. Version: ${data.data?.latest || 'unknown'}`);
      return data.success;
    } catch (error) {
      console.error('❌ Failed to connect to Raydium API:', error);
      return false;
    }
  }

  /**
   * 计算交换路径和预期输出
   */
  async computeSwap(
    inputMint: string,
    outputMint: string,
    amount: string,
    slippageBps: number = config.slippageBps
  ): Promise<SwapComputeResponse> {
    const url = `${this.baseUrl}/compute/swap-base-in`;
    const params = new URLSearchParams({
      inputMint,
      outputMint,
      amount,
      slippageBps: slippageBps.toString(),
    });

    console.log(`🔍 Computing swap: ${url}?${params.toString()}`);

    const response = await fetch(`${url}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to compute swap: ${response.status} ${response.statusText}\n${errorText}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(`Raydium API error: ${JSON.stringify(data)}`);
    }

    return data;
  }

  /**
   * 获取交换指令
   */
  async getSwapInstruction(
    swapRequest: SwapRequest,
    computeUnitPriceMicroLamports?: number
  ): Promise<SwapInstructionResponse> {
    const url = `${this.baseUrl}/compute/swap-base-in`;

    const requestBody: SwapInstructionRequest = {
      swapRequest,
      computeUnitPriceMicroLamports,
    };

    console.log(`📝 Getting swap instruction: ${url}`);
    console.log(`Request body:`, JSON.stringify(requestBody, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get swap instruction: ${response.status} ${response.statusText}\n${errorText}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(`Raydium API error: ${JSON.stringify(data)}`);
    }

    return data;
  }

  /**
   * 获取池信息
   */
  async getPoolInfo(mint1: string, mint2?: string): Promise<any> {
    const url = `${this.baseUrl}/pools/info/mint`;
    const params = new URLSearchParams({
      mint1,
      poolType: 'all',
      poolSortField: 'liquidity',
      sortType: 'desc',
      pageSize: '10',
      page: '1',
    });

    if (mint2) {
      params.append('mint2', mint2);
    }

    console.log(`🏊 Getting pool info: ${url}?${params.toString()}`);

    const response = await fetch(`${url}?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get pool info: ${response.status} ${response.statusText}\n${errorText}`);
    }

    const data = await response.json();

    if (!data.success) {
      throw new Error(`Raydium API error: ${JSON.stringify(data)}`);
    }

    return data;
  }
}
