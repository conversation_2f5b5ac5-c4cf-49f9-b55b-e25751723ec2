import { SwapService } from './swap';
import { config, validateConfig } from './config';

async function main() {
  console.log('🚀 SolQuant TypeScript - SOL to USDC Swap on Raydium');
  console.log('================================================\n');

  try {
    // 验证配置
    validateConfig();
    console.log('✅ Configuration validated');

    // 创建交换服务
    const swapService = new SwapService();

    // 检查交换前状态
    await swapService.checkPreSwapStatus();

    // 询问用户是否继续
    const shouldContinue = await askUserConfirmation();
    if (!shouldContinue) {
      console.log('❌ Swap cancelled by user');
      return;
    }

    // 执行交换
    const signature = await swapService.swapSOLToUSDC(config.swapAmount);
    
    console.log(`\n🎉 Swap completed successfully!`);
    console.log(`Transaction signature: ${signature}`);

    // 检查交换后状态
    await swapService.checkPostSwapStatus();

  } catch (error) {
    console.error('\n❌ Error occurred:', error);
    process.exit(1);
  }
}

/**
 * 询问用户确认
 */
async function askUserConfirmation(): Promise<boolean> {
  console.log(`\n⚠️  You are about to swap ${config.swapAmount} SOL to USDC on Solana devnet`);
  console.log(`Slippage tolerance: ${config.slippageBps / 100}%`);
  console.log(`\nThis is a REAL transaction on devnet. Make sure you have enough devnet SOL.`);
  console.log(`Get devnet SOL from: https://faucet.solana.com/\n`);

  // 在生产环境中，你可能想要添加一个真正的用户输入提示
  // 这里我们自动继续，但在实际使用中应该添加用户确认
  console.log(`🤖 Auto-confirming for demo purposes...`);
  
  return true;
}

/**
 * 处理未捕获的异常
 */
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// 运行主程序
if (require.main === module) {
  main();
}
