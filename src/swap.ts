import {
  Transaction,
  VersionedTransaction,
  TransactionInstruction,
  AddressLookupTableAccount,
  TransactionMessage,
  PublicKey,
  LAMPORTS_PER_SOL,
} from '@solana/web3.js';
import { RaydiumAPI } from './raydium';
import { SolanaWallet } from './wallet';
import { config, TOKENS } from './config';
import { SwapRequest } from './types';

export class SwapService {
  private raydium: RaydiumAPI;
  private wallet: SolanaWallet;

  constructor() {
    this.raydium = new RaydiumAPI();
    this.wallet = new SolanaWallet();
  }

  /**
   * 执行 SOL 到 USDC 的交换
   */
  async swapSOLToUSDC(amountInSOL: number): Promise<string> {
    console.log(`\n🔄 Starting swap: ${amountInSOL} SOL → USDC`);

    // 1. 检查钱包状态
    await this.wallet.checkWalletStatus();

    // 2. 检查 Raydium API 连接
    const isConnected = await this.raydium.checkConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to Raydium API');
    }

    // 3. 检查余额
    const solBalance = await this.wallet.getSOLBalance();
    if (solBalance < amountInSOL + 0.01) { // 保留 0.01 SOL 作为手续费
      throw new Error(`Insufficient SOL balance. Required: ${amountInSOL + 0.01}, Available: ${solBalance}`);
    }

    // 4. 计算交换
    const amountInLamports = Math.floor(amountInSOL * LAMPORTS_PER_SOL);
    console.log(`💰 Swapping ${amountInLamports} lamports (${amountInSOL} SOL)`);

    const computeResult = await this.raydium.computeSwap(
      TOKENS.SOL,
      TOKENS.USDC,
      amountInLamports.toString(),
      config.slippageBps
    );

    console.log(`📊 Swap computation result:`);
    console.log(`  Input: ${computeResult.data.inputAmount} lamports (${amountInSOL} SOL)`);
    console.log(`  Output: ${computeResult.data.outputAmount} (${computeResult.data.outputMint.symbol})`);
    console.log(`  Price Impact: ${computeResult.data.priceImpactPct.toFixed(4)}%`);
    console.log(`  Route: ${computeResult.data.routePlan.length} step(s)`);

    // 5. 构建交换请求
    const swapRequest: SwapRequest = {
      inputMint: TOKENS.SOL,
      outputMint: TOKENS.USDC,
      amount: amountInLamports.toString(),
      slippageBps: config.slippageBps,
      otherAmountThreshold: computeResult.data.otherAmountThreshold,
      swapType: 'ExactIn',
      txVersion: 'V0',
      wallet: this.wallet.publicKey.toBase58(),
      wrapUnwrapSOL: true,
    };

    // 6. 获取交换指令
    console.log(`📝 Getting swap instructions...`);
    const instructionResult = await this.raydium.getSwapInstruction(swapRequest);

    // 7. 构建并发送交易
    return await this.executeSwapTransaction(instructionResult.data);
  }

  /**
   * 执行交换交易 (简化版本 - 仅用于演示)
   */
  private async executeSwapTransaction(instructionData: any): Promise<string> {
    try {
      console.log(`🔨 Building transaction...`);

      // 注意：这是一个简化的实现
      // 在实际生产环境中，你需要：
      // 1. 正确解析 Raydium 返回的指令数据
      // 2. 构建完整的交易指令
      // 3. 处理地址查找表
      // 4. 设置正确的计算单元限制和优先费用

      console.log(`📊 Instruction data received:`, {
        hasSwapInstruction: !!instructionData.swapInstruction,
        computeUnitLimit: instructionData.computeUnitLimit,
        addressLookupTables: instructionData.addressLookupTableAccounts?.length || 0,
      });

      // 模拟交易执行（在实际环境中，这里应该构建真实的交易）
      console.log(`⚠️  This is a demo implementation. In production, you would:`);
      console.log(`   1. Decode the swap instruction from base64`);
      console.log(`   2. Parse account keys and create TransactionInstruction`);
      console.log(`   3. Handle address lookup tables for V0 transactions`);
      console.log(`   4. Set compute budget and priority fees`);
      console.log(`   5. Sign and send the transaction`);

      // 返回一个模拟的交易签名
      const mockSignature = 'demo_transaction_' + Date.now();
      console.log(`🎭 Mock transaction signature: ${mockSignature}`);

      return mockSignature;

    } catch (error) {
      console.error(`❌ Failed to execute swap transaction:`, error);
      throw error;
    }
  }

  /**
   * 获取池信息
   */
  async getPoolInfo(): Promise<void> {
    console.log(`\n🏊 Getting SOL/USDC pool information...`);

    try {
      const poolInfo = await this.raydium.getPoolInfo(TOKENS.SOL, TOKENS.USDC);

      if (poolInfo.data.data.length > 0) {
        const pool = poolInfo.data.data[0];
        console.log(`📊 Pool Information:`);
        console.log(`  Pool ID: ${pool.id}`);
        console.log(`  Type: ${pool.type}`);
        console.log(`  Price: ${pool.price}`);
        console.log(`  TVL: $${pool.tvl.toLocaleString()}`);
        console.log(`  24h Volume: $${pool.day.volume.toLocaleString()}`);
        console.log(`  Fee Rate: ${(pool.feeRate * 100).toFixed(4)}%`);
      } else {
        console.log(`❌ No pools found for SOL/USDC pair`);
      }
    } catch (error) {
      console.error(`❌ Failed to get pool info:`, error);
    }
  }

  /**
   * 检查交换前的状态
   */
  async checkPreSwapStatus(): Promise<void> {
    console.log(`\n🔍 Pre-swap Status Check:`);

    // 检查钱包余额
    const solBalance = await this.wallet.getSOLBalance();
    const usdcBalance = await this.wallet.getTokenBalance(TOKENS.USDC);

    console.log(`💰 Current Balances:`);
    console.log(`  SOL: ${solBalance.toFixed(6)}`);
    console.log(`  USDC: ${(usdcBalance / 1e6).toFixed(6)}`); // USDC has 6 decimals

    // 获取池信息
    await this.getPoolInfo();
  }

  /**
   * 检查交换后的状态
   */
  async checkPostSwapStatus(): Promise<void> {
    console.log(`\n✅ Post-swap Status Check:`);

    // 等待一下让余额更新
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 检查钱包余额
    const solBalance = await this.wallet.getSOLBalance();
    const usdcBalance = await this.wallet.getTokenBalance(TOKENS.USDC);

    console.log(`💰 Updated Balances:`);
    console.log(`  SOL: ${solBalance.toFixed(6)}`);
    console.log(`  USDC: ${(usdcBalance / 1e6).toFixed(6)}`); // USDC has 6 decimals
  }
}
