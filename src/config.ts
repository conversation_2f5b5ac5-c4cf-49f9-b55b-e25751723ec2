import dotenv from 'dotenv';

dotenv.config();

export interface Config {
  rpcUrl: string;
  privateKey: string;
  raydiumApiUrl: string;
  slippageBps: number;
  swapAmount: number;
}

export const config: Config = {
  rpcUrl: process.env.RPC_URL || 'https://api.devnet.solana.com',
  privateKey: process.env.PRIVATE_KEY || '',
  raydiumApiUrl: process.env.RAYDIUM_API_URL || 'https://api-v3-devnet.raydium.io',
  slippageBps: parseInt(process.env.SLIPPAGE_BPS || '50'),
  swapAmount: parseFloat(process.env.SWAP_AMOUNT || '0.1'),
};

// Token addresses for devnet
export const TOKENS = {
  SOL: 'So11111111111111111111111111111111111111112',
  USDC: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU', // USDC on devnet
  WSOL: 'So11111111111111111111111111111111111111112',
} as const;

export function validateConfig(): void {
  if (!config.privateKey) {
    throw new Error('PRIVATE_KEY environment variable is required');
  }
  
  if (!config.rpcUrl) {
    throw new Error('RPC_URL environment variable is required');
  }
  
  if (config.slippageBps < 1 || config.slippageBps > 10000) {
    throw new Error('SLIPPAGE_BPS must be between 1 and 10000');
  }
  
  if (config.swapAmount <= 0) {
    throw new Error('SWAP_AMOUNT must be greater than 0');
  }
}
