import {
  Connection,
  Keypair,
  PublicKey,
  Transaction,
  VersionedTransaction,
  sendAndConfirmTransaction,
  LAMPORTS_PER_SOL,
} from '@solana/web3.js';
import {
  getAssociatedTokenAddress,
  getAccount,
  TokenAccountNotFoundError,
  TokenInvalidAccountOwnerError,
} from '@solana/spl-token';
import { config } from './config';
import bs58 from 'bs58';

export class SolanaWallet {
  public connection: Connection;
  public keypair: Keypair;
  public publicKey: PublicKey;

  constructor() {
    this.connection = new Connection(config.rpcUrl, 'confirmed');
    
    try {
      // 尝试从 base58 解码私钥
      const secretKey = bs58.decode(config.privateKey);
      this.keypair = Keypair.fromSecretKey(secretKey);
    } catch (error) {
      // 如果 base58 解码失败，尝试从 JSON 数组解码
      try {
        const secretKey = JSON.parse(config.privateKey);
        this.keypair = Keypair.fromSecretKey(new Uint8Array(secretKey));
      } catch (jsonError) {
        throw new Error('Invalid private key format. Please provide either base58 string or JSON array.');
      }
    }
    
    this.publicKey = this.keypair.publicKey;
    console.log(`💼 Wallet loaded: ${this.publicKey.toBase58()}`);
  }

  /**
   * 获取 SOL 余额
   */
  async getSOLBalance(): Promise<number> {
    const balance = await this.connection.getBalance(this.publicKey);
    return balance / LAMPORTS_PER_SOL;
  }

  /**
   * 获取代币余额
   */
  async getTokenBalance(mintAddress: string): Promise<number> {
    try {
      const mint = new PublicKey(mintAddress);
      const tokenAccount = await getAssociatedTokenAddress(mint, this.publicKey);
      
      const accountInfo = await getAccount(this.connection, tokenAccount);
      return Number(accountInfo.amount);
    } catch (error) {
      if (error instanceof TokenAccountNotFoundError || error instanceof TokenInvalidAccountOwnerError) {
        return 0;
      }
      throw error;
    }
  }

  /**
   * 检查钱包状态
   */
  async checkWalletStatus(): Promise<void> {
    console.log(`\n📊 Wallet Status:`);
    console.log(`Address: ${this.publicKey.toBase58()}`);
    
    const solBalance = await this.getSOLBalance();
    console.log(`SOL Balance: ${solBalance.toFixed(6)} SOL`);
    
    if (solBalance < 0.01) {
      console.warn(`⚠️  Low SOL balance! You need SOL for transaction fees.`);
      console.log(`Please get some devnet SOL from: https://faucet.solana.com/`);
    }
  }

  /**
   * 发送并确认交易
   */
  async sendAndConfirmTransaction(transaction: Transaction): Promise<string> {
    try {
      console.log(`📤 Sending transaction...`);
      
      const signature = await sendAndConfirmTransaction(
        this.connection,
        transaction,
        [this.keypair],
        {
          commitment: 'confirmed',
          maxRetries: 3,
        }
      );
      
      console.log(`✅ Transaction confirmed: ${signature}`);
      console.log(`🔗 View on Solana Explorer: https://explorer.solana.com/tx/${signature}?cluster=devnet`);
      
      return signature;
    } catch (error) {
      console.error(`❌ Transaction failed:`, error);
      throw error;
    }
  }

  /**
   * 发送并确认版本化交易
   */
  async sendAndConfirmVersionedTransaction(transaction: VersionedTransaction): Promise<string> {
    try {
      console.log(`📤 Sending versioned transaction...`);
      
      // 签名交易
      transaction.sign([this.keypair]);
      
      // 发送交易
      const signature = await this.connection.sendTransaction(transaction, {
        maxRetries: 3,
      });
      
      // 等待确认
      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
      
      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${JSON.stringify(confirmation.value.err)}`);
      }
      
      console.log(`✅ Versioned transaction confirmed: ${signature}`);
      console.log(`🔗 View on Solana Explorer: https://explorer.solana.com/tx/${signature}?cluster=devnet`);
      
      return signature;
    } catch (error) {
      console.error(`❌ Versioned transaction failed:`, error);
      throw error;
    }
  }

  /**
   * 获取最新的区块哈希
   */
  async getLatestBlockhash() {
    return await this.connection.getLatestBlockhash('confirmed');
  }
}
