{"name": "toformat", "version": "2.0.0", "description": "Adds a `toFormat` instance method to decimal.js or big.js", "main": "toFormat.js", "devDependencies": {"big.js": "*", "decimal.js": ">=5.0.0"}, "scripts": {"test": "node test", "build": "uglifyjs toFormat.js -c -m -o toFormat.min.js --preamble \"/* toFormat.js v2.0.0 https://github.com/MikeMcl/toFormat */\""}, "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/toFormat.git"}, "keywords": ["format", "bignumber", "big", "number", "big.js", "decimal.js"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/MikeMcl/toFormat/issues"}, "homepage": "https://github.com/MikeMcl/toFormat#readme", "files": ["toFormat.js", "toFormat.min.js"]}