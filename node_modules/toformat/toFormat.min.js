/* toFormat.js v2.0.0 https://github.com/MikeMcl/toFormat */
function toFormat(r){"use strict";return r.prototype.toFormat=function(r,o,t){if(!this.e&&0!==this.e)return this.toString();var e,a,i,p,u,c,s,n,S,f,d,m,g,l,z,G=this.format||{},h=this.constructor.format||{};if(r!=u?"object"==typeof r?(t=r,r=u):o!=u?"object"==typeof o?(t=o,o=u):"object"!=typeof t&&(t={}):t={}:t={},e=this.toFixed(r,o).split("."),n=e[0],S=e[1],s=this.s<0?n.slice(1):n,c=s.length,f=t.decimalSeparator,f==u&&(f=G.decimalSeparator,f==u&&(f=h.decimalSeparator,f==u&&(f="."))),d=t.groupSeparator,d==u&&(d=G.groupSeparator,d==u&&(d=h.groupSeparator)),d&&(m=t.groupSize,m==u&&(m=G.groupSize,m==u&&(m=h.groupSize,m==u&&(m=0))),g=t.secondaryGroupSize,g==u&&(g=G.secondaryGroupSize,g==u&&(g=h.secondaryGroupSize,g==u&&(g=0))),g?(a=+g,i=+m,c-=i):(a=+m,i=+g),a>0&&c>0)){for(p=c%a||a,n=s.substr(0,p);c>p;p+=a)n+=d+s.substr(p,a);i>0&&(n+=d+s.slice(p)),this.s<0&&(n="-"+n)}return S?(l=t.fractionGroupSeparator,l==u&&(l=G.fractionGroupSeparator,l==u&&(l=h.fractionGroupSeparator)),l&&(z=t.fractionGroupSize,z==u&&(z=G.fractionGroupSize,z==u&&(z=h.fractionGroupSize,z==u&&(z=0))),z=+z,z&&(S=S.replace(new RegExp("\\d{"+z+"}\\B","g"),"$&"+l))),n+f+S):n},r.format={decimalSeparator:".",groupSeparator:",",groupSize:3,secondaryGroupSize:0,fractionGroupSeparator:"",fractionGroupSize:0},r}"undefined"!=typeof module&&module.exports&&(module.exports=toFormat);