{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/errors.ts"], "names": [], "mappings": "AAAA,4BAA4B;AAC5B,MAAM,OAAgB,UAAW,SAAQ,KAAK;IAC1C,YAAY,OAAgB;QACxB,KAAK,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;CACJ;AAED,gEAAgE;AAChE,MAAM,OAAO,yBAA0B,SAAQ,UAAU;IAAzD;;QACI,SAAI,GAAG,2BAA2B,CAAC;IACvC,CAAC;CAAA;AAED,+DAA+D;AAC/D,MAAM,OAAO,wBAAyB,SAAQ,UAAU;IAAxD;;QACI,SAAI,GAAG,0BAA0B,CAAC;IACtC,CAAC;CAAA;AAED,oEAAoE;AACpE,MAAM,OAAO,4BAA6B,SAAQ,UAAU;IAA5D;;QACI,SAAI,GAAG,8BAA8B,CAAC;IAC1C,CAAC;CAAA;AAED,mFAAmF;AACnF,MAAM,OAAO,6BAA8B,SAAQ,UAAU;IAA7D;;QACI,SAAI,GAAG,+BAA+B,CAAC;IAC3C,CAAC;CAAA;AAED,4FAA4F;AAC5F,MAAM,OAAO,4BAA6B,SAAQ,UAAU;IAA5D;;QACI,SAAI,GAAG,8BAA8B,CAAC;IAC1C,CAAC;CAAA;AAED,4EAA4E;AAC5E,MAAM,OAAO,qBAAsB,SAAQ,UAAU;IAArD;;QACI,SAAI,GAAG,uBAAuB,CAAC;IACnC,CAAC;CAAA;AAED,8EAA8E;AAC9E,MAAM,OAAO,sBAAuB,SAAQ,UAAU;IAAtD;;QACI,SAAI,GAAG,wBAAwB,CAAC;IACpC,CAAC;CAAA;AAED,gFAAgF;AAChF,MAAM,OAAO,uBAAwB,SAAQ,UAAU;IAAvD;;QACI,SAAI,GAAG,yBAAyB,CAAC;IACrC,CAAC;CAAA;AAED,oDAAoD;AACpD,MAAM,OAAO,mCAAoC,SAAQ,UAAU;IAAnE;;QACI,SAAI,GAAG,qCAAqC,CAAC;IACjD,CAAC;CAAA;AAED,kDAAkD;AAClD,MAAM,OAAO,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAED,iDAAiD;AACjD,MAAM,OAAO,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAED,iDAAiD;AACjD,MAAM,OAAO,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAED,qEAAqE;AACrE,MAAM,OAAO,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAED,mFAAmF;AACnF,MAAM,OAAO,gCAAiC,SAAQ,UAAU;IAAhE;;QACI,SAAI,GAAG,kCAAkC,CAAC;IAC9C,CAAC;CAAA;AAED,0EAA0E;AAC1E,MAAM,OAAO,4BAA6B,SAAQ,UAAU;IAA5D;;QACI,SAAI,GAAG,8BAA8B,CAAC;IAC1C,CAAC;CAAA;AAED,gGAAgG;AAChG,MAAM,OAAO,oCAAqC,SAAQ,UAAU;IAApE;;QACI,SAAI,GAAG,sCAAsC,CAAC;IAClD,CAAC;CAAA"}