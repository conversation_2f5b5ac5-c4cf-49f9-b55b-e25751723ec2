{"version": 3, "file": "immutableOwner.js", "sourceRoot": "", "sources": ["../../../src/extensions/immutableOwner.ts"], "names": [], "mappings": ";;;AAAA,yDAA+C;AAE/C,yDAAqE;AAKrE,kDAAkD;AACrC,QAAA,oBAAoB,GAAG,IAAA,sBAAM,EAAiB,EAAE,CAAC,CAAC;AAElD,QAAA,oBAAoB,GAAG,4BAAoB,CAAC,IAAI,CAAC;AAE9D,SAAgB,iBAAiB,CAAC,OAAgB;IAC9C,MAAM,aAAa,GAAG,IAAA,mCAAgB,EAAC,gCAAa,CAAC,cAAc,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;IACtF,IAAI,aAAa,KAAK,IAAI,EAAE,CAAC;QACzB,OAAO,4BAAoB,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;IACtD,CAAC;SAAM,CAAC;QACJ,OAAO,IAAI,CAAC;IAChB,CAAC;AACL,CAAC;AAPD,8CAOC"}