"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.encodeToCurve = exports.hashToCurve = exports.secp256r1 = exports.p256 = void 0;
const nist_ts_1 = require("./nist.js");
/** @deprecated use `import { p256 } from '@noble/curves/nist.js';` */
exports.p256 = nist_ts_1.p256;
/** @deprecated use `import { p256 } from '@noble/curves/nist.js';` */
exports.secp256r1 = nist_ts_1.p256;
/** @deprecated use `import { p256_hasher } from '@noble/curves/nist.js';` */
exports.hashToCurve = (() => nist_ts_1.p256_hasher.hashToCurve)();
/** @deprecated use `import { p256_hasher } from '@noble/curves/nist.js';` */
exports.encodeToCurve = (() => nist_ts_1.p256_hasher.encodeToCurve)();
//# sourceMappingURL=p256.js.map