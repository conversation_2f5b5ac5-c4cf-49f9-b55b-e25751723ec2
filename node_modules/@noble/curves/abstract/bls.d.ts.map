{"version": 3, "file": "bls.d.ts", "sourceRoot": "", "sources": ["../src/abstract/bls.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;IAeI;AACJ,sEAAsE;AACtE,OAAO,EAKL,KAAK,KAAK,EACV,KAAK,GAAG,EACR,KAAK,OAAO,EACb,MAAM,aAAa,CAAC;AAErB,OAAO,EAEL,KAAK,SAAS,EACd,KAAK,WAAW,EAChB,KAAK,OAAO,EAEZ,KAAK,YAAY,EACjB,KAAK,UAAU,EAChB,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EAAoC,KAAK,MAAM,EAAE,MAAM,cAAc,CAAC;AAC7E,OAAO,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,YAAY,CAAC;AACrE,OAAO,EAGL,KAAK,cAAc,EACnB,KAAK,eAAe,EACpB,KAAK,gBAAgB,EACrB,KAAK,oBAAoB,EAC1B,MAAM,kBAAkB,CAAC;AAE1B,KAAK,EAAE,GAAG,MAAM,CAAC;AAKjB,MAAM,MAAM,SAAS,GAAG,gBAAgB,GAAG,UAAU,CAAC;AAEtD,MAAM,MAAM,mBAAmB,CAAC,EAAE,IAAI;IACpC,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;IAC3C,gCAAgC;IAChC,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;CACrD,CAAC;AAEF,MAAM,MAAM,cAAc,CAAC,EAAE,IAAI;IAC/B,SAAS,CAAC,KAAK,EAAE,UAAU,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACnD,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACxC,OAAO,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;IACjD,KAAK,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;IAC3C,gCAAgC;IAChC,UAAU,CAAC,KAAK,EAAE,gBAAgB,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;CACrD,CAAC;AAEF,MAAM,MAAM,SAAS,GAAG;IACtB,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;IACf,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,GAAG,EAAE,MAAM,CAAC;IACZ,GAAG,EAAE,MAAM,CAAC;IACZ,IAAI,EAAE,OAAO,CAAC;CACf,CAAC;AAEF,MAAM,MAAM,wBAAwB,GAAG,CACrC,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,KACJ;IAAE,EAAE,EAAE,GAAG,CAAC;IAAC,EAAE,EAAE,GAAG,CAAC;IAAC,EAAE,EAAE,GAAG,CAAA;CAAE,CAAC;AACnC,MAAM,MAAM,gBAAgB,GAAG,CAC7B,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,EAAE,EAAE,GAAG,EACP,QAAQ,EAAE,wBAAwB,KAC/B,IAAI,CAAC;AACV,MAAM,MAAM,UAAU,GAAG;IACvB,IAAI,EAAE,OAAO,CAAC;IACd,sBAAsB,EAAE,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAG,CAAC,KAAK,UAAU,CAAC;IACjE,eAAe,EAAE,CAAC,KAAK,EAAE,CAAC,UAAU,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC;IACzD,OAAO,EAAE,CAAC,CAAC,EAAE,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAAE,iBAAiB,CAAC,EAAE,OAAO,KAAK,IAAI,CAAC;IAClG,YAAY,EAAE,CACZ,KAAK,EAAE;QAAE,EAAE,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC;QAAC,EAAE,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAA;KAAE,EAAE,EAChE,iBAAiB,CAAC,EAAE,OAAO,KACxB,IAAI,CAAC;CACX,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAI7B,WAAW,EAAE,MAAM,CAAC;IACpB,SAAS,EAAE,OAAO,CAAC;IACnB,SAAS,EAAE,SAAS,CAAC;IAErB,cAAc,CAAC,EAAE,gBAAgB,CAAC;CACnC,CAAC;AACF,MAAM,MAAM,SAAS,GAAG;IACtB,EAAE,EAAE,eAAe,CAAC,EAAE,CAAC,GAAG;QACxB,cAAc,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC;QACnC,UAAU,EAAE,UAAU,CAAC,EAAE,CAAC,CAAC;QAC3B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,EAAE,EAAE,eAAe,CAAC,GAAG,CAAC,GAAG;QACzB,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;QAC/B,UAAU,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5B,WAAW,EAAE,OAAO,CAAC;KACtB,CAAC;IACF,MAAM,EAAE,SAAS,CAAC;IAClB,MAAM,EAAE;QAIN,WAAW,EAAE,gBAAgB,CAAC,aAAa,CAAC,CAAC;QAC7C,SAAS,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;QACzC,CAAC,EAAE,MAAM,CAAC;QACV,SAAS,EAAE,gBAAgB,CAAC,WAAW,CAAC,CAAC;KAC1C,CAAC;IACF,WAAW,EAAE,OAAO,CAAC;IACrB,IAAI,EAAE,KAAK,CAAC;IACZ,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,MAAM,KAAK,UAAU,CAAC;IAEnD,cAAc,CAAC,EAAE,gBAAgB,CAAC;CACnC,CAAC;AAEF,KAAK,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;AAC1C,KAAK,UAAU,GAAG,gBAAgB,EAAE,CAAC;AAErC;;;;GAIG;AACH,MAAM,WAAW,YAAY;IAC3B,cAAc,EAAE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACrC,eAAe,EAAE,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IACtC,eAAe,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;IAC/C,OAAO,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;IAC/B,YAAY,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;IACzC,EAAE,EAAE;QAAE,KAAK,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAA;KAAE,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IAC5D,EAAE,EAAE;QAAE,KAAK,EAAE,oBAAoB,CAAC,GAAG,CAAC,CAAA;KAAE,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IAC1D,MAAM,EAAE;QACN,EAAE,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;QACf,GAAG,EAAE,MAAM,CAAC;QACZ,GAAG,EAAE,MAAM,CAAC;QACZ,IAAI,EAAE,OAAO,CAAC;QACd,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;KACpB,CAAC;IACF,KAAK,EAAE;QACL,eAAe,EAAE,MAAM,UAAU,CAAC;QAClC,sCAAsC;QACtC,gBAAgB,EAAE,MAAM,UAAU,CAAC;QACnC,sBAAsB,EAAE,UAAU,CAAC,wBAAwB,CAAC,CAAC;KAC9D,CAAC;CACH;AAED,MAAM,MAAM,OAAO,GAAG,YAAY,GAAG;IACnC,oDAAoD;IACpD,YAAY,EAAE,CAAC,SAAS,EAAE,OAAO,KAAK,UAAU,CAAC;IACjD,qDAAqD;IACrD,8BAA8B,EAAE,CAAC,SAAS,EAAE,OAAO,KAAK,UAAU,CAAC;IACnE,4CAA4C;IAC5C,IAAI,EAAE;QACJ,CAAC,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACvE,CACE,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAC9B,SAAS,EAAE,OAAO,EAClB,OAAO,CAAC,EAAE,YAAY,GACrB,gBAAgB,CAAC,GAAG,CAAC,CAAC;KAC1B,CAAC;IACF,6CAA6C;IAC7C,kBAAkB,EAAE;QAClB,CAAC,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,UAAU,CAAC;QACvE,CACE,OAAO,EAAE,gBAAgB,CAAC,EAAE,CAAC,EAC7B,SAAS,EAAE,OAAO,EAClB,OAAO,CAAC,EAAE,YAAY,GACrB,gBAAgB,CAAC,EAAE,CAAC,CAAC;KACzB,CAAC;IACF,8CAA8C;IAC9C,MAAM,EAAE,CACN,SAAS,EAAE,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,EACtC,OAAO,EAAE,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,EACpC,SAAS,EAAE,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,EACrC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,+CAA+C;IAC/C,oBAAoB,EAAE,CACpB,SAAS,EAAE,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,EACrC,OAAO,EAAE,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,EACnC,SAAS,EAAE,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,EACtC,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,WAAW,EAAE,CACX,SAAS,EAAE,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,EACtC,QAAQ,EAAE,CAAC,GAAG,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,EACzC,UAAU,EAAE,CAAC,GAAG,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE,EAC1C,OAAO,CAAC,EAAE,YAAY,KACnB,OAAO,CAAC;IACb,2DAA2D;IAC3D,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;KAC5D,CAAC;IACF,2DAA2D;IAC3D,mBAAmB,EAAE;QACnB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,gBAAgB,CAAC,GAAG,CAAC,EAAE,GAAG,gBAAgB,CAAC,GAAG,CAAC,CAAC;KAC9D,CAAC;IACF,4DAA4D;IAC5D,wBAAwB,EAAE;QACxB,CAAC,UAAU,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC;QAChC,CAAC,UAAU,EAAE,gBAAgB,CAAC,EAAE,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;KAC5D,CAAC;IACF,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;IACvC,EAAE,EAAE,cAAc,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;IACzC,iDAAiD;IACjD,SAAS,EAAE,cAAc,CAAC,GAAG,CAAC,CAAC;IAC/B,kDAAkD;IAClD,cAAc,EAAE,mBAAmB,CAAC,EAAE,CAAC,CAAC;IACxC,MAAM,EAAE;QACN,WAAW,EAAE,MAAM,CAAC;QACpB,CAAC,EAAE,MAAM,CAAC;QACV,SAAS,EAAE,SAAS,CAAC;QACrB,kBAAkB;QAClB,GAAG,EAAE,MAAM,CAAC;QACZ,kBAAkB;QAClB,GAAG,EAAE,GAAG,CAAC;KACV,CAAC;CACH,CAAC;AAEF,KAAK,QAAQ,GAAG,GAAG,GAAG,UAAU,CAAC;AACjC,MAAM,WAAW,OAAO,CAAC,CAAC,EAAE,CAAC;IAC3B,YAAY,CAAC,SAAS,EAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACtD,IAAI,CAAC,aAAa,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAClF,MAAM,CACJ,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,EACzC,OAAO,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAC5B,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,GACxC,OAAO,CAAC;IACX,WAAW,EAAE,CACX,SAAS,EAAE,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,EACzC,QAAQ,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,EAC/B,UAAU,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,KAC3C,OAAO,CAAC;IACb,mBAAmB,CAAC,UAAU,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACzF,mBAAmB,CAAC,UAAU,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IACzF,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,EAAE,QAAQ,CAAC,EAAE,WAAW,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;IAClG,SAAS,EAAE,cAAc,CAAC,CAAC,CAAC,CAAC;CAC9B;AA6SD,wBAAgB,GAAG,CAAC,KAAK,EAAE,SAAS,GAAG,OAAO,CAiL7C"}