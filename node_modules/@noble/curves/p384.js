"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.encodeToCurve = exports.hashToCurve = exports.secp384r1 = exports.p384 = void 0;
const nist_ts_1 = require("./nist.js");
/** @deprecated use `import { p384 } from '@noble/curves/nist.js';` */
exports.p384 = nist_ts_1.p384;
/** @deprecated use `import { p384 } from '@noble/curves/nist.js';` */
exports.secp384r1 = nist_ts_1.p384;
/** @deprecated use `import { p384_hasher } from '@noble/curves/nist.js';` */
exports.hashToCurve = (() => nist_ts_1.p384_hasher.hashToCurve)();
/** @deprecated use `import { p384_hasher } from '@noble/curves/nist.js';` */
exports.encodeToCurve = (() => nist_ts_1.p384_hasher.encodeToCurve)();
//# sourceMappingURL=p384.js.map