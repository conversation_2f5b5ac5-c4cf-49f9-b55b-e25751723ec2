/**
 * NIST secp521r1 aka p521.
 * @module
 */
/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */
import { type H2CMethod } from './abstract/hash-to-curve.ts';
import { p521 as p521n } from './nist.ts';
/** @deprecated use `import { p521 } from '@noble/curves/nist.js';` */
export declare const p521: typeof p521n;
/** @deprecated use `import { p521 } from '@noble/curves/nist.js';` */
export declare const secp521r1: typeof p521n;
/** @deprecated use `import { p521_hasher } from '@noble/curves/nist.js';` */
export declare const hashToCurve: H2CMethod<bigint>;
/** @deprecated use `import { p521_hasher } from '@noble/curves/nist.js';` */
export declare const encodeToCurve: H2CMethod<bigint>;
//# sourceMappingURL=p521.d.ts.map