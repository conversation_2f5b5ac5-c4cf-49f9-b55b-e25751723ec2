/**
 * NIST secp384r1 aka p384.
 * @module
 */
/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */
import {} from "./abstract/hash-to-curve.js";
import { p384_hasher, p384 as p384n } from "./nist.js";
/** @deprecated use `import { p384 } from '@noble/curves/nist.js';` */
export const p384 = p384n;
/** @deprecated use `import { p384 } from '@noble/curves/nist.js';` */
export const secp384r1 = p384n;
/** @deprecated use `import { p384_hasher } from '@noble/curves/nist.js';` */
export const hashToCurve = /* @__PURE__ */ (() => p384_hasher.hashToCurve)();
/** @deprecated use `import { p384_hasher } from '@noble/curves/nist.js';` */
export const encodeToCurve = /* @__PURE__ */ (() => p384_hasher.encodeToCurve)();
//# sourceMappingURL=p384.js.map