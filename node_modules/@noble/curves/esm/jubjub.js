/**
 * @deprecated
 * @module
 */
import { jubjub_findGroupHash, jubjub_groupHash, jubjub as jubjubn } from "./misc.js";
/** @deprecated use `import { jubjub } from '@noble/curves/misc.js';` */
export const jubjub = jubjubn;
/** @deprecated use `import { jubjub_findGroupHash } from '@noble/curves/misc.js';` */
export const findGroupHash = jubjub_findGroupHash;
/** @deprecated use `import { jubjub_groupHash } from '@noble/curves/misc.js';` */
export const groupHash = jubjub_groupHash;
//# sourceMappingURL=jubjub.js.map