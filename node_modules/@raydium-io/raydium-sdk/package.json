{"name": "@raydium-io/raydium-sdk", "version": "1.3.1-beta.58", "description": "An SDK for building applications on top of Raydium.", "license": "GPL-3.0", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.mjs", "typings": "./dist/index.d.ts", "repository": "https://github.com/raydium-io/raydium-sdk", "keywords": ["raydium", "solana"], "lint-staged": {"{src,test,misc}/**/*.ts": ["yarn lint"]}, "scripts": {"lint": "eslint src/**/*.ts", "test": "jest", "build-docs": "typedoc", "build-docs-watch": "typedoc --watch", "build": "tsup src/index.ts --dts --format esm,cjs", "push": "yarn build && yarn publish", "prepare": "husky install"}, "peerDependencies": {"@solana/web3.js": "^1.73.0"}, "dependencies": {"@solana/buffer-layout": "^4.0.1", "@solana/spl-token": "^0.3.9", "axios": "^1.6.2", "big.js": "^6.2.1", "bn.js": "^5.2.1", "decimal.js": "^10.4.3", "decimal.js-light": "^2.5.1", "fecha": "^4.2.3", "lodash": "^4.17.21", "toformat": "^2.0.0", "tsup": "^8.1.0"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/preset-env": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@solana/spl-token-registry": "^0.2.4574", "@types/big.js": "^6.2.2", "@types/bn.js": "^5.1.5", "@types/jest": "^29.5.10", "@types/node": "^20.9.4", "@types/node-fetch": "^2.6.9", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "ajv": "^8.12.0", "babel-jest": "^29.7.0", "chalk": "^5.3.0", "consola": "^3.2.3", "dotenv": "^16.3.1", "eslint": "^8.54.0", "eslint-plugin-tsdoc": "^0.2.17", "got": "^13.0.0", "husky": "^8.0.3", "jest": "^29.7.0", "lint-staged": "^15.1.0", "npm-check-updates": "^16.14.11", "shx": "^0.3.4", "ts-node": "^10.9.1", "typedoc": "^0.25.3", "typescript": "^5.3.2"}}