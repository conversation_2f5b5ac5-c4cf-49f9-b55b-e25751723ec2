import * as _solana_web3_js from '@solana/web3.js';
import { PublicKey, Commitment, Connection, AccountInfo, TransactionInstruction, Transaction, AddressLookupTableAccount, Signer, VersionedTransaction, EpochInfo } from '@solana/web3.js';
export { SYSVAR_CLOCK_PUBKEY, SYSVAR_RENT_PUBKEY } from '@solana/web3.js';
import * as BN from 'bn.js';
import BN__default from 'bn.js';
import { TransferFeeConfig, Mint } from '@solana/spl-token';
export { ASSOCIATED_TOKEN_PROGRAM_ID, TOKEN_PROGRAM_ID } from '@solana/spl-token';
import Big, { BigSource, RoundingMode } from 'big.js';
import Decimal, { Numeric } from 'decimal.js-light';
import Decimal$1 from 'decimal.js';

type BigNumberish = BN__default | string | number | bigint;
declare function parseBigNumberish(value: BigNumberish): BN__default;
declare function tenExponentiate(shift: BigNumberish): BN__default;
declare function divCeil(a: BN__default, b: BN__default): BN__default;

declare enum Rounding {
    ROUND_DOWN = 0,
    ROUND_HALF_UP = 1,
    ROUND_UP = 2
}
declare const ZERO: BN__default;
declare const ONE: BN__default;
declare const TWO: BN__default;
declare const THREE: BN__default;
declare const FIVE: BN__default;
declare const TEN: BN__default;
declare const _100: BN__default;
declare const _1000: BN__default;
declare const _10000: BN__default;

type Primitive = boolean | number | string | null | undefined | PublicKey;
/**
 *
 * @example
 * ```typescript
 * interface A {
 *   keyA: string;
 *   keyB: string;
 *   map: {
 *     hello: string;
 *     i: number;
 *   };
 *   list: (string | number)[];
 *   keyC: number;
 * }
 *
 * type WrappedA = ReplaceType<A, string, boolean> // {
 *   keyA: boolean;
 *   keyB: boolean;
 *   map: {
 *     hello: boolean;
 *     i: number;
 *   };
 *   list: (number | boolean)[];
 *   keyC: number;
 * }
 * ```
 */
type ReplaceType<Old, From, To> = {
    [T in keyof Old]: Old[T] extends From ? Exclude<Old[T], From> | To : Old[T] extends Primitive ? From extends Old[T] ? Exclude<Old[T], From> | To : Old[T] : ReplaceType<Old[T], From, To>;
};
declare function jsonInfo2PoolKeys<T>(jsonInfo: T): ReplaceType<T, string, PublicKey>;
declare function poolKeys2JsonInfo<T>(jsonInfo: T): ReplaceType<T, PublicKey, string>;

declare function inspectPublicKey(): void;
declare function inspectBN(): void;
declare function inspectAll(): void;

interface JsonFileMetaData {
    readonly name: string;
    readonly timestamp: string;
    readonly version: {
        major: number;
        minor: number;
        patch: number;
    };
}

/**
 * https://youmightnotneed.com/lodash/
 */
declare function chunkArray<T>(arr: T[], chunkSize?: number, cache?: T[][]): T[][];
declare function intersection<T>(arr: T[], ...args: T[][]): T[];
declare function xor<T>(arr: T[], ...args: T[][]): T[];
declare function uniq<T>(arr: T[]): T[];

declare enum LogLevel {
    DEBUG = "DEBUG",
    INFO = "INFO",
    WARNING = "WARNING",
    ERROR = "ERROR",
    OFF = "OFF"
}
declare enum ErrorCode {
    UNKNOWN_ERROR = "UNKNOWN_ERROR",
    NOT_IMPLEMENTED = "NOT_IMPLEMENTED",
    UNSUPPORTED_OPERATION = "UNSUPPORTED_OPERATION",
    NETWORK_ERROR = "NETWORK_ERROR",
    RPC_ERROR = "RPC_ERROR",
    TIMEOUT = "TIMEOUT",
    BUFFER_OVERRUN = "BUFFER_OVERRUN",
    NUMERIC_FAULT = "NUMERIC_FAULT",
    MISSING_NEW = "MISSING_NEW",
    INVALID_ARGUMENT = "INVALID_ARGUMENT",
    MISSING_ARGUMENT = "MISSING_ARGUMENT",
    UNEXPECTED_ARGUMENT = "UNEXPECTED_ARGUMENT",
    INSUFFICIENT_FUNDS = "INSUFFICIENT_FUNDS"
}
declare class Logger {
    readonly version: string;
    readonly moduleName: string;
    static errors: typeof ErrorCode;
    static levels: typeof LogLevel;
    constructor(moduleName: string);
    _log(logLevel: LogLevel, args: Array<any>): void;
    debug(...args: Array<any>): void;
    info(...args: Array<any>): void;
    warn(...args: Array<any>): void;
    makeError(message: string, code?: ErrorCode, params?: any): Error;
    throwError(message: string, code?: ErrorCode, params?: any): never;
    throwArgumentError(message: string, name: string, value: any): never;
    assert(condition: any, message: string, code?: ErrorCode, params?: any): void;
    assertArgument(condition: any, message: string, name: string, value: any): void;
    checkNormalize(message?: string): void;
    checkSafeUint53(value: number, message?: string): void;
    checkArgumentCount(count: number, expectedCount: number, message?: string): void;
    checkNew(target: any, kind: any): void;
    checkAbstract(target: any, kind: any): void;
    static globalLogger(): Logger;
    static setCensorship(censorship: boolean, permanent?: boolean): void;
    static setLogLevel(moduleName: string, logLevel: 'DEBUG' | 'INFO' | 'WARNING' | 'ERROR' | 'OFF'): void;
    static from(version: string): Logger;
}

declare const SYSTEM_PROGRAM_ID: PublicKey;
declare const MEMO_PROGRAM_ID: PublicKey;
declare const RENT_PROGRAM_ID: PublicKey;
declare const METADATA_PROGRAM_ID: PublicKey;
declare const INSTRUCTION_PROGRAM_ID: PublicKey;
type PublicKeyish = PublicKey | string;
declare function validateAndParsePublicKey(publicKey: PublicKeyish): PublicKey;
declare function findProgramAddress(seeds: Array<Buffer | Uint8Array>, programId: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function AccountMeta(publicKey: PublicKey, isSigner: boolean): {
    pubkey: PublicKey;
    isWritable: boolean;
    isSigner: boolean;
};
declare function AccountMetaReadonly(publicKey: PublicKey, isSigner: boolean): {
    pubkey: PublicKey;
    isWritable: boolean;
    isSigner: boolean;
};

type Without<T, U> = {
    [P in Exclude<keyof T, keyof U>]?: never;
};
type XOR<T, U> = T | U extends object ? (Without<T, U> & U) | (Without<U, T> & T) : T | U;

interface GetMultipleAccountsInfoConfig {
    batchRequest?: boolean;
    commitment?: Commitment;
}
declare function getMultipleAccountsInfo(connection: Connection, publicKeys: PublicKey[], config?: GetMultipleAccountsInfoConfig): Promise<(AccountInfo<Buffer> | null)[]>;
declare function getMultipleAccountsInfoWithCustomFlags<T extends {
    pubkey: PublicKey;
}>(connection: Connection, publicKeysWithCustomFlag: T[], config?: GetMultipleAccountsInfoConfig): Promise<({
    accountInfo: AccountInfo<Buffer> | null;
} & T)[]>;
interface GetTokenAccountsByOwnerConfig {
    commitment?: Commitment;
}
/**
 * Forecast transaction size
 */
declare function forecastTransactionSize(instructions: TransactionInstruction[], signers: PublicKey[]): boolean;
/**
 * Simulates multiple instruction
 */
declare function simulateMultipleInstruction(connection: Connection, instructions: TransactionInstruction[], keyword: string, batchRequest?: boolean): Promise<string[]>;
declare function parseSimulateLogToJson(log: string, keyword: string): string;
declare function parseSimulateValue(log: string, key: string): string;
declare function simulateTransaction(connection: Connection, transactions: Transaction[], batchRequest?: boolean): Promise<any[]>;
declare function splitTxAndSigners<T extends TxVersion>({ connection, makeTxVersion, innerTransaction, lookupTableCache, computeBudgetConfig, payer, }: {
    connection: Connection;
    makeTxVersion: T;
    innerTransaction: InnerTransaction[];
    lookupTableCache?: CacheLTA;
    computeBudgetConfig?: ComputeBudgetConfig;
    payer: PublicKey;
}): Promise<(T extends typeof TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[]>;
declare const MAX_BASE64_SIZE = 1644;
interface CacheLTA {
    [key: string]: AddressLookupTableAccount;
}
declare function getMultipleLookupTableInfo({ connection, address, }: {
    connection: Connection;
    address: PublicKey[];
}): Promise<CacheLTA>;

/**
 * A currency is any fungible financial instrument on Solana, including SOL and all SPL tokens.
 *
 * The only instance of the base class `Currency` is SOL.
 */
declare class Currency {
    readonly symbol?: string;
    readonly name?: string;
    readonly decimals: number;
    /**
     * The only instance of the base class `Currency`.
     */
    static readonly SOL: Currency;
    /**
     * Constructs an instance of the base class `Currency`. The only instance of the base class `Currency` is `Currency.SOL`.
     * @param decimals - decimals of the currency
     * @param symbol - symbol of the currency
     * @param name - name of the currency
     */
    constructor(decimals: number, symbol?: string, name?: string);
}
declare function inspectCurrency(): void;
/**
 * Represents an SPL token with a unique address and some metadata.
 */
declare class Token extends Currency {
    readonly programId: PublicKey;
    readonly mint: PublicKey;
    /**
     * The only instance of the base class `Token`.
     */
    static readonly WSOL: Token;
    constructor(programId: PublicKeyish, mint: PublicKeyish, decimals: number, symbol?: string, name?: string);
    /**
     * Returns true if the two tokens are equivalent, i.e. have the same mint address.
     * @param other - other token to compare
     */
    equals(other: Token): boolean;
}
declare function inspectToken(): void;
/**
 * Compares two currencies for equality
 */
declare function currencyEquals(currencyA: Currency, currencyB: Currency): boolean;

declare class Fraction {
    readonly numerator: BN__default;
    readonly denominator: BN__default;
    constructor(numerator: BigNumberish, denominator?: BigNumberish);
    get quotient(): BN__default;
    invert(): Fraction;
    add(other: Fraction | BigNumberish): Fraction;
    sub(other: Fraction | BigNumberish): Fraction;
    mul(other: Fraction | BigNumberish): Fraction;
    div(other: Fraction | BigNumberish): Fraction;
    toSignificant(significantDigits: number, format?: object, rounding?: Rounding): string;
    toFixed(decimalPlaces: number, format?: object, rounding?: Rounding): string;
}

declare function splitNumber(num: string, decimals: number): string[];
declare class CurrencyAmount extends Fraction {
    readonly currency: Currency;
    constructor(currency: Currency, amount: BigNumberish, isRaw?: boolean);
    get raw(): BN__default;
    isZero(): boolean;
    /**
     * a greater than b
     */
    gt(other: CurrencyAmount): boolean;
    /**
     * a less than b
     */
    lt(other: CurrencyAmount): boolean;
    add(other: CurrencyAmount): CurrencyAmount;
    sub(other: CurrencyAmount): CurrencyAmount;
    toSignificant(significantDigits?: number, format?: object, rounding?: Rounding): string;
    /**
     * To fixed
     *
     * @example
     * ```
     * 1 -> 1.000000000
     * 1.234 -> 1.234000000
     * 1.123456789876543 -> 1.123456789
     * ```
     */
    toFixed(decimalPlaces?: number, format?: object, rounding?: Rounding): string;
    /**
     * To exact
     *
     * @example
     * ```
     * 1 -> 1
     * 1.234 -> 1.234
     * 1.123456789876543 -> 1.123456789
     * ```
     */
    toExact(format?: object): string;
}
declare class TokenAmount extends CurrencyAmount {
    readonly token: Token;
    constructor(token: Token, amount: BigNumberish, isRaw?: boolean);
    add(other: TokenAmount): TokenAmount;
    subtract(other: TokenAmount): TokenAmount;
}
type TokenAmountType = CurrencyAmount | TokenAmount;

declare const _100_PERCENT: Fraction;
declare class Percent extends Fraction {
    toSignificant(significantDigits?: number, format?: object, rounding?: Rounding): string;
    toFixed(decimalPlaces?: number, format?: object, rounding?: Rounding): string;
}

declare class Price extends Fraction {
    readonly baseCurrency: Currency;
    readonly quoteCurrency: Currency;
    readonly scalar: Fraction;
    constructor(baseCurrency: Currency, denominator: BigNumberish, quoteCurrency: Currency, numerator: BigNumberish);
    get raw(): Fraction;
    get adjusted(): Fraction;
    invert(): Price;
    mul(other: Price): Price;
    toSignificant(significantDigits?: number, format?: object, rounding?: Rounding): string;
    toFixed(decimalPlaces?: number, format?: object, rounding?: Rounding): string;
}

interface FormatOptions {
    decimalSeparator?: string;
    groupSeparator?: string;
    groupSize?: number;
    fractionGroupSeparator?: string;
    fractionGroupSize?: number;
}
interface WrappedBig extends Big {
    add(n: BigSource): WrappedBig;
    abs(): WrappedBig;
    div(n: BigSource): WrappedBig;
    minus(n: BigSource): WrappedBig;
    mod(n: BigSource): WrappedBig;
    mul(n: BigSource): WrappedBig;
    plus(n: BigSource): WrappedBig;
    pow(exp: number): WrappedBig;
    round(dp?: number, rm?: RoundingMode): WrappedBig;
    sqrt(): WrappedBig;
    sub(n: BigSource): WrappedBig;
    times(n: BigSource): WrappedBig;
    toFormat(): string;
    toFormat(options: FormatOptions): string;
    toFormat(fractionLength: number): string;
    toFormat(fractionLength: number, options: FormatOptions): string;
    toFormat(fractionLength: number, missionUnknown: number): string;
    toFormat(fractionLength: number, missionUnknown: number, options: FormatOptions): string;
}
interface WrappedDecimal extends Decimal {
    absoluteValue(): WrappedDecimal;
    abs(): WrappedDecimal;
    dividedBy(y: Numeric): WrappedDecimal;
    div(y: Numeric): WrappedDecimal;
    dividedToIntegerBy(y: Numeric): WrappedDecimal;
    idiv(y: Numeric): WrappedDecimal;
    logarithm(base?: Numeric): WrappedDecimal;
    log(base?: Numeric): WrappedDecimal;
    minus(y: Numeric): WrappedDecimal;
    sub(y: Numeric): WrappedDecimal;
    modulo(y: Numeric): WrappedDecimal;
    mod(y: Numeric): WrappedDecimal;
    naturalExponetial(): WrappedDecimal;
    exp(): WrappedDecimal;
    naturalLogarithm(): WrappedDecimal;
    ln(): WrappedDecimal;
    negated(): WrappedDecimal;
    neg(): WrappedDecimal;
    plus(y: Numeric): WrappedDecimal;
    add(y: Numeric): WrappedDecimal;
    squareRoot(): WrappedDecimal;
    sqrt(): WrappedDecimal;
    times(y: Numeric): WrappedDecimal;
    mul(y: Numeric): WrappedDecimal;
    toWrappedDecimalPlaces(dp?: number, rm?: number): WrappedDecimal;
    todp(dp?: number, rm?: number): WrappedDecimal;
    toInteger(): WrappedDecimal;
    toint(): WrappedDecimal;
    toPower(y: Numeric): WrappedDecimal;
    pow(y: Numeric): WrappedDecimal;
    toSignificantDigits(sd?: number, rm?: number): WrappedDecimal;
    tosd(sd?: number, rm?: number): WrappedDecimal;
    toFormat(options: FormatOptions): string;
    toFormat(fractionLength: number): string;
    toFormat(fractionLength: number, options: FormatOptions): string;
    toFormat(fractionLength: number, missionUnknown: number): string;
    toFormat(fractionLength: number, missionUnknown: number, options: FormatOptions): string;
}

interface LayoutConstructor {
    new <T, P>(): Layout<T, P>;
    new <T, P>(span?: T, property?: P): Layout<T, P>;
    readonly prototype: Layout;
}
interface Layout<T = any, P = ''> {
    span: number;
    property?: P;
    decode(b: Buffer, offset?: number): T;
    encode(src: T, b: Buffer, offset?: number): number;
    getSpan(b: Buffer, offset?: number): number;
    replicate<AP extends string>(name: AP): Layout<T, AP>;
}
declare const Layout: LayoutConstructor;
interface StructureConstructor {
    new <T = any, P = '', DecodeSchema extends {
        [key: string]: any;
    } = any>(): Structure$1<T, P, DecodeSchema>;
    new <T = any, P = '', DecodeSchema extends {
        [key: string]: any;
    } = any>(fields: T, property?: P, decodePrefixes?: boolean): Structure$1<T, P, DecodeSchema>;
}
interface Structure$1<T = any, P = '', DecodeSchema extends {
    [key: string]: any;
} = any> extends Layout<DecodeSchema, P> {
    span: number;
    decode(b: Buffer, offset?: number): DecodeSchema;
    layoutFor<AP extends string>(property: AP): Layout<DecodeSchema[AP]>;
    offsetOf<AP extends string>(property: AP): number;
}
declare const Structure$1: StructureConstructor;
interface UnionConstructor {
    new <UnionSchema extends {
        [key: string]: any;
    } = any>(): Union$1<UnionSchema>;
    new <UnionSchema extends {
        [key: string]: any;
    } = any>(discr: Layout<any, any>, defaultLayout: Layout<any, any>, property?: string): Union$1<UnionSchema>;
}
interface Union$1<UnionSchema extends {
    [key: string]: any;
} = any> extends Layout {
    registry: object;
    decode(b: Buffer, offset?: number): Partial<UnionSchema>;
    addVariant(variant: number, layout: Structure$1<any, any, Partial<UnionSchema>> | Layout<any, keyof UnionSchema>, property?: string): any;
}
declare const Union$1: UnionConstructor;
interface BitStructureConstructor {
    new (...params: any[]): BitStructure;
}
type BitStructure<T = unknown, P = ''> = Layout<T, P>;
declare const BitStructure: BitStructureConstructor;
interface UIntConstructor {
    new <T, P>(span?: T, property?: P): UInt<T, P>;
}
type UInt<T = any, P = ''> = Layout<T, P>;
declare const UInt: UIntConstructor;
interface BlobConstructor {
    new (...params: ConstructorParameters<LayoutConstructor>): Blob;
}
type Blob<P extends string = ''> = Layout<Buffer, P>;
declare const Blob: BlobConstructor;
declare const greedy: <P extends string = "">(elementSpan?: number, property?: P | undefined) => Layout<number, P>;
declare const u16: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u24: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u40: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u48: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const nu64: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u16be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u24be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u32be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u40be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const u48be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const nu64be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s8: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s16: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s24: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s32: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s40: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s48: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const ns64: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s16be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s24be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s32be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s40be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const s48be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const ns64be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const f32: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const f32be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const f64: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const f64be: <P extends string = "">(property?: P | undefined) => Layout<number, P>;
declare const unionLayoutDiscriminator: <P extends string = "">(layout: Layout<any, P>, property?: P | undefined) => any;
declare const blob: <P extends string = "">(length: number | Layout<number, P>, property?: P | undefined) => Blob<P>;
declare const cstr: <P extends string = "">(property?: P | undefined) => Layout<string, P>;
declare const utf8: <P extends string = "">(maxSpan: number, property?: P | undefined) => Layout<string, P>;
declare const bits: <T, P extends string = "">(word: Layout<T, "">, msb?: boolean, property?: P | undefined) => BitStructure<T, P>;
declare const offset: <T, P extends string = "">(layout: Layout<T, P>, offset?: number, property?: P | undefined) => Layout<T, P>;
type GetStructureSchema<T extends Structure$1> = T extends Structure$1<any, any, infer S> ? S : unknown;

declare class BNLayout<P extends string = ''> extends Layout<BN__default, P> {
    blob: Layout<Buffer>;
    signed: boolean;
    constructor(span: number, signed: boolean, property?: P);
    /** @override */
    decode(b: Buffer, offset?: number): BN__default;
    /** @override */
    encode(src: BN__default, b: Buffer, offset?: number): number;
}
declare class WideBits<P extends string = ''> extends Layout<Record<string, boolean>, P> {
    _lower: any;
    _upper: any;
    constructor(property?: P);
    addBoolean(property: string): void;
    decode(b: Buffer, offset?: number): Record<string, boolean>;
    encode(src: any, b: Buffer, offset?: number): any;
}
declare function u8<P extends string = ''>(property?: P): UInt<number, P>;
declare function u32<P extends string = ''>(property?: P): UInt<number, P>;
declare function u64<P extends string = ''>(property?: P): BNLayout<P>;
declare function u128<P extends string = ''>(property?: P): BNLayout<P>;
declare function i8<P extends string = ''>(property?: P): BNLayout<P>;
declare function i64<P extends string = ''>(property?: P): BNLayout<P>;
declare function i128<P extends string = ''>(property?: P): BNLayout<P>;
declare class WrappedLayout<T, U, P extends string = ''> extends Layout<U, P> {
    layout: Layout<T>;
    decoder: (data: T) => U;
    encoder: (src: U) => T;
    constructor(layout: Layout<T>, decoder: (data: T) => U, encoder: (src: U) => T, property?: P);
    decode(b: Buffer, offset?: number): U;
    encode(src: U, b: Buffer, offset?: number): number;
    getSpan(b: Buffer, offset?: number): number;
}
declare function publicKey<P extends string = ''>(property?: P): Layout<PublicKey, P>;
declare class OptionLayout<T, P> extends Layout<T | null, P> {
    layout: Layout<T>;
    discriminator: Layout<number>;
    constructor(layout: Layout<T>, property?: P);
    encode(src: T | null, b: Buffer, offset?: number): number;
    decode(b: Buffer, offset?: number): T | null;
    getSpan(b: Buffer, offset?: number): number;
}
declare function option<T, P extends string = ''>(layout: Layout<T>, property?: P): Layout<T | null, P>;
declare function bool<P extends string = ''>(property?: P): Layout<boolean, P>;
declare function decodeBool(value: number): boolean;
declare function encodeBool(value: boolean): number;
declare function vec<T, P extends string = ''>(elementLayout: Layout<T>, property?: P): Layout<T[], P>;
declare function tagged<T, P extends string = ''>(tag: BN__default, layout: Layout<T>, property?: P): Layout<T, P>;
declare function vecU8<P extends string = ''>(property?: P): Layout<Buffer, P>;
declare function str<P extends string = ''>(property?: P): Layout<string, P>;
interface EnumLayout<T, P extends string = ''> extends Layout<T, P> {
    registry: Record<string, Layout<any>>;
}
declare function rustEnum<T, P extends string = ''>(variants: Layout<any>[], property?: P): EnumLayout<T, P>;
declare function array<T, P extends string = ''>(elementLayout: Layout<T>, length: number, property?: P): Layout<T[], P>;
declare class Structure<T, P, D extends {
    [key: string]: any;
} = any> extends Structure$1<T, P, D> {
    /** @override */
    decode(b: Buffer, offset?: number): D;
}
declare function struct<T, P extends string = ''>(fields: T, property?: P, decodePrefixes?: boolean): T extends Layout<infer Value, infer Property>[] ? Structure<Value, P, {
    [K in Exclude<Extract<Property, string>, ''>]: Extract<T[number], Layout<any, K>> extends Layout<infer V, any> ? V : any;
}> : any;
type GetLayoutSchemaFromStructure<T extends Structure<any, any, any>> = T extends Structure<any, any, infer S> ? S : any;
type GetStructureFromLayoutSchema<S extends {
    [key: string]: any;
} = any> = Structure<any, any, S>;
declare class Union<Schema extends {
    [key: string]: any;
} = any> extends Union$1<Schema> {
    encodeInstruction(instruction: any): Buffer;
    decodeInstruction(instruction: any): Partial<Schema>;
}
declare function union<UnionSchema extends {
    [key: string]: any;
} = any>(discr: any, defaultLayout?: any, property?: string): Union<UnionSchema>;
declare class Zeros extends Blob {
    decode(b: Buffer, offset: number): Buffer;
}
declare function zeros(length: number): Zeros;
declare function seq<T, P extends string = '', AnotherP extends string = ''>(elementLayout: Layout<T, P>, count: number | BN__default | Layout<BN__default | number, P>, property?: AnotherP): Layout<T[], AnotherP>;

declare const SPL_MINT_LAYOUT: Structure<number | _solana_web3_js.PublicKey | BN, "", {
    decimals: number;
    mintAuthorityOption: number;
    mintAuthority: _solana_web3_js.PublicKey;
    supply: BN;
    isInitialized: number;
    freezeAuthorityOption: number;
    freezeAuthority: _solana_web3_js.PublicKey;
}>;
type SplMintLayout = typeof SPL_MINT_LAYOUT;
type SplMint = GetStructureSchema<SplMintLayout>;
declare const SPL_ACCOUNT_LAYOUT: Structure<number | _solana_web3_js.PublicKey | BN, "", {
    owner: _solana_web3_js.PublicKey;
    state: number;
    mint: _solana_web3_js.PublicKey;
    amount: BN;
    delegateOption: number;
    delegate: _solana_web3_js.PublicKey;
    isNativeOption: number;
    isNative: BN;
    delegatedAmount: BN;
    closeAuthorityOption: number;
    closeAuthority: _solana_web3_js.PublicKey;
}>;
type SplAccountLayout = typeof SPL_ACCOUNT_LAYOUT;
type SplAccount = GetStructureSchema<SplAccountLayout>;

declare class Spl {
    static getAssociatedTokenAccount({ mint, owner, programId, }: {
        mint: PublicKey;
        owner: PublicKey;
        programId: PublicKey;
    }): PublicKey;
    static makeCreateAssociatedTokenAccountInstruction({ programId, mint, associatedAccount, owner, payer, instructionsType, }: {
        programId: PublicKey;
        mint: PublicKey;
        associatedAccount: PublicKey;
        owner: PublicKey;
        payer: PublicKey;
        instructionsType: InstructionType[];
    }): TransactionInstruction;
    static makeCreateWrappedNativeAccountInstructions({ connection, owner, payer, amount, commitment, }: {
        connection: Connection;
        owner: PublicKey;
        payer: PublicKey;
        amount: BigNumberish;
        commitment?: Commitment;
    }): Promise<{
        address: {
            newAccount: PublicKey;
        };
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    }>;
    static insertCreateWrappedNativeAccount({ connection, owner, payer, amount, instructions, instructionsType, signers, commitment, }: {
        connection: Connection;
        owner: PublicKey;
        payer: PublicKey;
        amount: BigNumberish;
        instructions: TransactionInstruction[];
        instructionsType: InstructionType[];
        signers: Signer[];
        commitment?: Commitment;
    }): Promise<PublicKey>;
    static makeInitMintInstruction({ programId, mint, decimals, mintAuthority, freezeAuthority, instructionTypes, }: {
        programId: PublicKey;
        mint: PublicKey;
        decimals: number;
        mintAuthority: PublicKey;
        freezeAuthority?: PublicKey | null;
        instructionTypes: InstructionType[];
    }): TransactionInstruction;
    static makeMintToInstruction({ programId, mint, dest, authority, amount, multiSigners, instructionTypes, }: {
        programId: PublicKey;
        mint: PublicKey;
        dest: PublicKey;
        authority: PublicKey;
        amount: BigNumberish;
        multiSigners?: Signer[];
        instructionTypes: InstructionType[];
    }): TransactionInstruction;
    static makeInitAccountInstruction({ programId, mint, tokenAccount, owner, instructionTypes, }: {
        programId: PublicKey;
        mint: PublicKey;
        tokenAccount: PublicKey;
        owner: PublicKey;
        instructionTypes: InstructionType[];
    }): TransactionInstruction;
    static makeTransferInstruction({ programId, source, destination, owner, amount, multiSigners, instructionsType, }: {
        programId: PublicKey;
        source: PublicKey;
        destination: PublicKey;
        owner: PublicKey;
        amount: BigNumberish;
        multiSigners?: Signer[];
        instructionsType: InstructionType[];
    }): TransactionInstruction;
    static makeCloseAccountInstruction({ programId, tokenAccount, owner, payer, multiSigners, instructionsType, }: {
        programId: PublicKey;
        tokenAccount: PublicKey;
        owner: PublicKey;
        payer: PublicKey;
        multiSigners?: Signer[];
        instructionsType: InstructionType[];
    }): TransactionInstruction;
    static createInitAccountInstruction(programId: PublicKey, mint: PublicKey, account: PublicKey, owner: PublicKey): TransactionInstruction;
}

interface ComputeBudgetConfig {
    units?: number;
    microLamports?: number;
}
interface InnerTransaction {
    instructionTypes: InstructionType[];
    instructions: TransactionInstruction[];
    signers: Signer[];
    lookupTableAddress?: PublicKey[];
}
interface MakeInstructionOutType<T extends {
    [name: string]: PublicKey;
} = {
    [name: string]: PublicKey;
}> {
    address: T;
    innerTransaction: InnerTransaction;
}
type InnerSimpleTransaction = InnerSimpleLegacyTransaction | InnerSimpleV0Transaction;
interface InnerSimpleLegacyTransaction {
    instructionTypes: InstructionType[];
    instructions: TransactionInstruction[];
    signers: Signer[];
}
interface InnerSimpleV0Transaction {
    instructionTypes: InstructionType[];
    instructions: TransactionInstruction[];
    signers: Signer[];
    lookupTableAddress?: CacheLTA;
}
declare enum TxVersion {
    'V0' = 0,
    'LEGACY' = 1
}
declare enum InstructionType {
    'createAccount' = 0,
    'initAccount' = 1,
    'createATA' = 2,
    'closeAccount' = 3,
    'transferAmount' = 4,
    'initMint' = 5,
    'mintTo' = 6,
    'initMarket' = 7,// create market main ins
    'util1216OwnerClaim' = 8,// owner claim token ins
    'setComputeUnitPrice' = 9,// addComputeBudget
    'setComputeUnitLimit' = 10,// addComputeBudget
    'clmmCreatePool' = 11,
    'clmmOpenPosition' = 12,
    'clmmIncreasePosition' = 13,
    'clmmDecreasePosition' = 14,
    'clmmClosePosition' = 15,
    'clmmSwapBaseIn' = 16,
    'clmmSwapBaseOut' = 17,
    'clmmInitReward' = 18,
    'clmmSetReward' = 19,
    'clmmCollectReward' = 20,
    'ammV4Swap' = 21,
    'ammV4AddLiquidity' = 22,
    'ammV4RemoveLiquidity' = 23,
    'ammV4SimulatePoolInfo' = 24,
    'ammV4SwapBaseIn' = 25,
    'ammV4SwapBaseOut' = 26,
    'ammV4CreatePool' = 27,
    'ammV4InitPool' = 28,
    'ammV4CreatePoolV2' = 29,
    'ammV5AddLiquidity' = 30,
    'ammV5RemoveLiquidity' = 31,
    'ammV5SimulatePoolInfo' = 32,
    'ammV5SwapBaseIn' = 33,
    'ammV5SwapBaseOut' = 34,
    'routeSwap' = 35,
    'routeSwap1' = 36,
    'routeSwap2' = 37,
    'farmV3Deposit' = 38,
    'farmV3Withdraw' = 39,
    'farmV3CreateLedger' = 40,
    'farmV5Deposit' = 41,
    'farmV5Withdraw' = 42,
    'farmV5CreateLedger' = 43,
    'farmV6Deposit' = 44,
    'farmV6Withdraw' = 45,
    'farmV6Create' = 46,
    'farmV6Restart' = 47,
    'farmV6CreatorAddReward' = 48,
    'farmV6CreatorWithdraw' = 49,
    'test' = 50
}

interface TokenAccount {
    programId: PublicKey;
    pubkey: PublicKey;
    accountInfo: SplAccount;
}
interface SelectTokenAccountParams {
    tokenAccounts: TokenAccount[];
    programId: PublicKey;
    mint: PublicKey;
    owner: PublicKey;
    config?: {
        associatedOnly?: boolean;
    };
}
interface HandleTokenAccountParams {
    connection: Connection;
    side: 'in' | 'out';
    amount: BigNumberish;
    programId: PublicKey;
    mint: PublicKey;
    tokenAccount: PublicKey | null;
    owner: PublicKey;
    payer?: PublicKey;
    frontInstructions: TransactionInstruction[];
    endInstructions?: TransactionInstruction[];
    frontInstructionsType: InstructionType[];
    endInstructionsType?: InstructionType[];
    signers: Signer[];
    bypassAssociatedCheck: boolean;
    checkCreateATAOwner: boolean;
}
interface SelectOrCreateTokenAccountParams {
    programId: PublicKey;
    mint: PublicKey;
    tokenAccounts: TokenAccount[];
    owner: PublicKey;
    createInfo?: {
        connection: Connection;
        payer: PublicKey;
        amount?: BigNumberish;
        frontInstructions: TransactionInstruction[];
        endInstructions?: TransactionInstruction[];
        signers: Signer[];
        frontInstructionsType: InstructionType[];
        endInstructionsType?: InstructionType[];
    };
    associatedOnly: boolean;
    checkCreateATAOwner: boolean;
}
interface UnsignedTransactionAndSigners {
    transaction: Transaction;
    signers: Signer[];
}
declare class Base {
    static _selectTokenAccount(params: SelectTokenAccountParams): PublicKey | null;
    static _handleTokenAccount(params: HandleTokenAccountParams): Promise<PublicKey>;
    static _selectOrCreateTokenAccount<T extends SelectOrCreateTokenAccountParams>(params: T): Promise<T['createInfo'] extends undefined ? PublicKey | undefined : PublicKey>;
}
declare function generatePubKey({ fromPublicKey, programId, }: {
    fromPublicKey: PublicKey;
    programId: PublicKey;
}): {
    publicKey: PublicKey;
    seed: string;
};

declare const LOOKUP_TABLE_CACHE: CacheLTA;

declare function getWSOLAmount({ tokenAccounts }: {
    tokenAccounts: TokenAccount[];
}): BN__default;
declare function unwarpSol({ ownerInfo, tokenAccounts, makeTxVersion, connection, }: {
    connection: Connection;
    ownerInfo: {
        wallet: PublicKey;
        payer: PublicKey;
    };
    tokenAccounts: TokenAccount[];
    makeTxVersion: TxVersion;
}): Promise<{
    address: {};
    innerTransactions: (InnerSimpleLegacyTransaction | InnerSimpleV0Transaction)[];
}>;
declare function buildSimpleTransaction({ connection, makeTxVersion, payer, innerTransactions, recentBlockhash, addLookupTableInfo, }: {
    makeTxVersion: TxVersion;
    payer: PublicKey;
    connection: Connection;
    innerTransactions: InnerSimpleTransaction[];
    recentBlockhash?: string | undefined;
    addLookupTableInfo?: CacheLTA | undefined;
}): Promise<(VersionedTransaction | Transaction)[]>;
declare function buildTransaction({ connection, makeTxVersion, payer, innerTransactions, recentBlockhash, lookupTableCache, }: {
    makeTxVersion: TxVersion;
    payer: PublicKey;
    connection: Connection;
    innerTransactions: InnerTransaction[];
    recentBlockhash?: string | undefined;
    lookupTableCache?: CacheLTA;
}): Promise<(VersionedTransaction | Transaction)[]>;
interface TransferAmountFee {
    amount: TokenAmount | CurrencyAmount;
    fee: TokenAmount | CurrencyAmount | undefined;
    expirationTime: number | undefined;
}
interface GetTransferAmountFee {
    amount: BN__default;
    fee: BN__default | undefined;
    expirationTime: number | undefined;
}
declare function getTransferAmountFee(amount: BN__default, feeConfig: TransferFeeConfig | undefined, epochInfo: EpochInfo, addFee: boolean): GetTransferAmountFee;
declare function minExpirationTime(expirationTime1: number | undefined, expirationTime2: number | undefined): number | undefined;
type ReturnTypeFetchMultipleMintInfo = Mint & {
    feeConfig: TransferFeeConfig | undefined;
};
interface ReturnTypeFetchMultipleMintInfos {
    [mint: string]: ReturnTypeFetchMultipleMintInfo;
}
declare function fetchMultipleMintInfos({ connection, mints }: {
    connection: Connection;
    mints: PublicKey[];
}): Promise<ReturnTypeFetchMultipleMintInfos>;
declare function BNDivCeil(bn1: BN__default, bn2: BN__default): BN__default;

declare const LIQUIDITY_STATE_LAYOUT_V4: Structure<_solana_web3_js.PublicKey | BN | BN[], "", {
    nonce: BN;
    owner: _solana_web3_js.PublicKey;
    status: BN;
    maxOrder: BN;
    depth: BN;
    baseDecimal: BN;
    quoteDecimal: BN;
    state: BN;
    resetFlag: BN;
    minSize: BN;
    volMaxCutRatio: BN;
    amountWaveRatio: BN;
    baseLotSize: BN;
    quoteLotSize: BN;
    minPriceMultiplier: BN;
    maxPriceMultiplier: BN;
    systemDecimalValue: BN;
    minSeparateNumerator: BN;
    minSeparateDenominator: BN;
    tradeFeeNumerator: BN;
    tradeFeeDenominator: BN;
    pnlNumerator: BN;
    pnlDenominator: BN;
    swapFeeNumerator: BN;
    swapFeeDenominator: BN;
    baseNeedTakePnl: BN;
    quoteNeedTakePnl: BN;
    quoteTotalPnl: BN;
    baseTotalPnl: BN;
    poolOpenTime: BN;
    punishPcAmount: BN;
    punishCoinAmount: BN;
    orderbookToInitTime: BN;
    swapBaseInAmount: BN;
    swapQuoteOutAmount: BN;
    swapBase2QuoteFee: BN;
    swapQuoteInAmount: BN;
    swapBaseOutAmount: BN;
    swapQuote2BaseFee: BN;
    baseVault: _solana_web3_js.PublicKey;
    quoteVault: _solana_web3_js.PublicKey;
    baseMint: _solana_web3_js.PublicKey;
    quoteMint: _solana_web3_js.PublicKey;
    lpMint: _solana_web3_js.PublicKey;
    openOrders: _solana_web3_js.PublicKey;
    marketId: _solana_web3_js.PublicKey;
    marketProgramId: _solana_web3_js.PublicKey;
    targetOrders: _solana_web3_js.PublicKey;
    withdrawQueue: _solana_web3_js.PublicKey;
    lpVault: _solana_web3_js.PublicKey;
    lpReserve: BN;
    padding: BN[];
}>;
type LiquidityStateLayoutV4 = typeof LIQUIDITY_STATE_LAYOUT_V4;
type LiquidityStateV4 = GetStructureSchema<LiquidityStateLayoutV4>;
declare const LIQUIDITY_STATE_LAYOUT_V5: Structure<_solana_web3_js.PublicKey | BN | BN[], "", {
    nonce: BN;
    owner: _solana_web3_js.PublicKey;
    status: BN;
    maxOrder: BN;
    depth: BN;
    baseDecimal: BN;
    quoteDecimal: BN;
    state: BN;
    resetFlag: BN;
    minSize: BN;
    volMaxCutRatio: BN;
    amountWaveRatio: BN;
    baseLotSize: BN;
    quoteLotSize: BN;
    minPriceMultiplier: BN;
    maxPriceMultiplier: BN;
    minSeparateNumerator: BN;
    minSeparateDenominator: BN;
    tradeFeeNumerator: BN;
    tradeFeeDenominator: BN;
    pnlNumerator: BN;
    pnlDenominator: BN;
    swapFeeNumerator: BN;
    swapFeeDenominator: BN;
    baseNeedTakePnl: BN;
    quoteNeedTakePnl: BN;
    quoteTotalPnl: BN;
    baseTotalPnl: BN;
    poolOpenTime: BN;
    punishPcAmount: BN;
    punishCoinAmount: BN;
    orderbookToInitTime: BN;
    swapBaseInAmount: BN;
    swapQuoteOutAmount: BN;
    swapBase2QuoteFee: BN;
    swapQuoteInAmount: BN;
    swapBaseOutAmount: BN;
    swapQuote2BaseFee: BN;
    baseVault: _solana_web3_js.PublicKey;
    quoteVault: _solana_web3_js.PublicKey;
    baseMint: _solana_web3_js.PublicKey;
    quoteMint: _solana_web3_js.PublicKey;
    lpMint: _solana_web3_js.PublicKey;
    openOrders: _solana_web3_js.PublicKey;
    marketId: _solana_web3_js.PublicKey;
    marketProgramId: _solana_web3_js.PublicKey;
    targetOrders: _solana_web3_js.PublicKey;
    padding: BN[];
    accountType: BN;
    systemDecimalsValue: BN;
    abortTradeFactor: BN;
    priceTickMultiplier: BN;
    priceTick: BN;
    modelDataAccount: _solana_web3_js.PublicKey;
}>;
type LiquidityStateLayoutV5 = typeof LIQUIDITY_STATE_LAYOUT_V5;
type LiquidityStateV5 = GetStructureSchema<LiquidityStateLayoutV5>;
type LiquidityState = LiquidityStateV4 | LiquidityStateV5;
type LiquidityStateLayout = LiquidityStateLayoutV4 | LiquidityStateLayoutV5;
declare const LIQUIDITY_VERSION_TO_STATE_LAYOUT: {
    [version: number]: LiquidityStateLayout;
};

type API_LINK = {
    time: string;
    info: string;
    pairs: string;
    price: string;
    rpcs: string;
    version: string;
    farmApr: string;
    farmAprLine: string;
    tokenInfo: string;
    poolInfo: string;
    farmInfo: string;
    idoInfo: string;
    idoProjectInfo: string;
    clmmPools: string;
    clmmConfigs: string;
    clmmPositionLine: string;
};
declare const ENDPOINT = "https://api.raydium.io";
declare const RAYDIUM_MAINNET: {
    readonly time: "/v2/main/chain/time";
    readonly info: "/v2/main/info";
    readonly pairs: "/v2/main/pairs";
    readonly price: "/v2/main/price";
    readonly rpcs: "/v2/main/rpcs";
    readonly version: "/v2/main/version";
    readonly farmApr: "/v2/main/farm/info";
    readonly farmAprLine: "/v2/main/farm-apr-tv";
    readonly tokenInfo: "/v2/sdk/token/raydium.mainnet.json";
    readonly poolInfo: "/v2/sdk/liquidity/mainnet.json";
    readonly dailyPoolInfo: "/v2/sdk/liquidity/date";
    readonly uiPoolInfo: "/v2/sdk/liquidity/mainnet.ui.json";
    readonly searchPool: "/v2/sdk/liquidity/mint/";
    readonly farmInfo: "/v2/sdk/farm-v2/mainnet.json";
    readonly idoInfo: "/v2/main/ido/pools";
    readonly idoProjectInfo: "/v2/main/ido/project/<id>";
    readonly clmmPools: "/v2/ammV3/ammPools";
    readonly clmmConfigs: "/v2/ammV3/ammConfigs";
    readonly clmmPositionLine: "/v2/ammV3/positionLine/<poolId>";
};

interface ApiTime {
    offset: number;
}
interface ApiInfo {
    tvl: number;
    volume24h: number;
    totalvolume: number;
}
interface ApiPairsItem {
    name: string;
    ammId: string;
    lpMint: string;
    market: string;
    liquidity: number;
    volume24h: number;
    volume24hQuote: number;
    fee24h: number;
    fee24hQuote: number;
    volume7d: number;
    volume7dQuote: number;
    fee7d: number;
    fee7dQuote: number;
    volume30d: number;
    volume30dQuote: number;
    fee30d: number;
    fee30dQuote: number;
    price: number;
    lpPrice: number;
    tokenAmountCoin: number;
    tokenAmountPc: number;
    tokenAmountLp: number;
    apr24h: number;
    apr7d: number;
    apr30d: number;
}
type ApiPairs = ApiPairsItem[];
interface ApiPrice {
    [mint: string]: number;
}
interface ApiRpcsItem {
    url: string;
    weight: number;
    batch: boolean;
    name: string;
}
interface ApiRpcs {
    rpcs: ApiRpcsItem[];
    strategy: 'weight' | 'speed';
}
interface ApiVersion {
    latest: string;
    least: string;
}
interface ApiFarmAprV3 {
    name: string;
    id: string;
    version: 3;
    apr: string;
    aprA: null;
    aprB: null;
    aprC: null;
    aprD: null;
    aprE: null;
    tvl: number;
    lpPrice: number;
}
interface ApiFarmAprV5 {
    name: string;
    id: string;
    version: 5;
    apr: string;
    aprA: string;
    aprB: string;
    aprC: null;
    aprD: null;
    aprE: null;
    tvl: number;
    lpPrice: number;
}
interface ApiFarmAprV6 {
    name: string;
    id: string;
    version: 3;
    apr: string;
    aprA: string;
    aprB: string;
    aprC: string;
    aprD: string;
    aprE: string;
    tvl: number;
    lpPrice: number;
}
type ApiFarmApr = ApiFarmAprV3 | ApiFarmAprV5 | ApiFarmAprV6;
interface ApiFarmAprLinePoint {
    time: number;
    apr: number;
}
interface ApiFarmAprLine {
    success: true;
    data: ApiFarmAprLinePoint[];
}
interface ApiTokenInfoItem {
    symbol: string;
    name: string;
    mint: string;
    decimals: number;
    extensions: {
        coingeckoId?: string;
        version?: 'TOKEN2022';
    };
    icon: string;
    hasFreeze: 0 | 1;
}
interface ApiTokenInfo {
    official: ApiTokenInfoItem[];
    unOfficial: ApiTokenInfoItem[];
    unNamed: {
        mint: string;
        decimals: number;
        hasFreeze: 0 | 1;
        extensions: {
            version?: 'TOKEN2022';
        };
    }[];
    blacklist: string[];
}
interface ApiPoolInfoV4 {
    id: string;
    baseMint: string;
    quoteMint: string;
    lpMint: string;
    baseDecimals: number;
    quoteDecimals: number;
    lpDecimals: number;
    version: 4;
    programId: string;
    authority: string;
    openOrders: string;
    targetOrders: string;
    baseVault: string;
    quoteVault: string;
    withdrawQueue: string;
    lpVault: string;
    marketVersion: 3;
    marketProgramId: string;
    marketId: string;
    marketAuthority: string;
    marketBaseVault: string;
    marketQuoteVault: string;
    marketBids: string;
    marketAsks: string;
    marketEventQueue: string;
    lookupTableAccount: string;
}
interface ApiPoolInfoV5 {
    id: string;
    baseMint: string;
    quoteMint: string;
    lpMint: string;
    baseDecimals: number;
    quoteDecimals: number;
    lpDecimals: number;
    version: 5;
    programId: string;
    authority: string;
    openOrders: string;
    targetOrders: string;
    baseVault: string;
    quoteVault: string;
    withdrawQueue: string;
    lpVault: string;
    marketVersion: 3;
    marketProgramId: string;
    marketId: string;
    marketAuthority: string;
    marketBaseVault: string;
    marketQuoteVault: string;
    marketBids: string;
    marketAsks: string;
    marketEventQueue: string;
    modelDataAccount: string;
    lookupTableAccount: string;
}
type ApiPoolInfoItem = ApiPoolInfoV4 | ApiPoolInfoV5;
interface ApiPoolInfo {
    official: ApiPoolInfoItem[];
    unOfficial: ApiPoolInfoItem[];
}
interface ApiFarmInfoV3 {
    id: string;
    symbol: string;
    lpMint: string;
    version: 3;
    programId: string;
    authority: string;
    lpVault: string;
    rewardInfos: [{
        rewardMint: string;
        rewardVault: string;
    }];
    upcoming: boolean;
}
interface ApiFarmInfoV5 {
    id: string;
    symbol: string;
    baseMint: string;
    quoteMint: string;
    lpMint: string;
    version: 5;
    programId: string;
    authority: string;
    lpVault: string;
    rewardInfos: [
        {
            rewardMint: string;
            rewardVault: string;
        },
        {
            rewardMint: string;
            rewardVault: string;
        }
    ];
    upcoming: boolean;
}
interface ApiFarmInfoV6 {
    id: string;
    symbol: string;
    baseMint: string;
    quoteMint: string;
    lpMint: string;
    version: 6;
    programId: string;
    authority: string;
    lpVault: string;
    rewardPeriodMax: number;
    rewardPeriodMin: number;
    rewardPeriodExtend: number;
    creator: string;
    rewardInfos: {
        rewardMint: string;
        rewardVault: string;
        rewardOpenTime: number;
        rewardEndTime: number;
        rewardPerSecond: number;
        rewardSender: string;
        rewardType: 'Standard SPL' | 'Option tokens';
    }[];
    upcoming: boolean;
}
interface ApiFarmInfo {
    stake: (ApiFarmInfoV3 | ApiFarmInfoV5 | ApiFarmInfoV6)[];
    raydium: (ApiFarmInfoV3 | ApiFarmInfoV5 | ApiFarmInfoV6)[];
    fusion: (ApiFarmInfoV3 | ApiFarmInfoV5 | ApiFarmInfoV6)[];
    ecosystem: (ApiFarmInfoV3 | ApiFarmInfoV5 | ApiFarmInfoV6)[];
}
interface ApiIdoInfoItem {
    id: string;
    authority: string;
    projectName: string;
    projectPosters: string;
    projectDetailLink: string;
    baseMint: string;
    baseVault: string;
    baseSymbol: string;
    baseDecimals: number;
    baseIcon: string;
    quoteMint: string;
    quoteVault: string;
    quoteSymbol: string;
    quoteDecimals: number;
    quoteIcon: string;
    startTime: number;
    endTime: number;
    startWithdrawTime: number;
    withdrawTimeQuote: number;
    stakeTimeEnd: number;
    stakeMinSize: number;
    price: number;
    raise: number;
    maxWinLotteries: number;
    raisedLotteries: number;
    isWinning: number;
    luckyNumbers: {
        digits: number;
        number: number;
        endRange: number;
    }[];
    version: 3;
    snapshotVersion: 1;
    programId: string;
    snapshotProgramId: string;
    seedId: string;
}
interface ApiIdoInfo {
    success: boolean;
    data: ApiIdoInfoItem[];
}
interface ApiIdoProjectInfo {
    info: ApiIdoInfoItem;
    projectInfo: {
        projectDetails: string;
        projectDocs: {
            [key: string]: string;
        };
        projectSocials: {
            [key: string]: string;
        };
    };
}
interface ApiClmmConfigItem {
    id: string;
    index: number;
    protocolFeeRate: number;
    tradeFeeRate: number;
    tickSpacing: number;
    fundFeeRate: number;
    fundOwner: string;
    description: string;
}
interface ApiClmmConfig {
    data: {
        [id: string]: ApiClmmConfigItem;
    };
}
interface ApiClmmPoolsItemStatistics {
    volume: number;
    volumeFee: number;
    feeA: number;
    feeB: number;
    feeApr: number;
    rewardApr: {
        A: number;
        B: number;
        C: number;
    };
    apr: number;
    priceMin: number;
    priceMax: number;
}
interface ApiClmmPoolsItem {
    id: string;
    mintProgramIdA: string;
    mintProgramIdB: string;
    mintA: string;
    mintB: string;
    vaultA: string;
    vaultB: string;
    mintDecimalsA: number;
    mintDecimalsB: number;
    ammConfig: ApiClmmConfigItem;
    rewardInfos: {
        mint: string;
        programId: string;
    }[];
    tvl: number;
    day: ApiClmmPoolsItemStatistics;
    week: ApiClmmPoolsItemStatistics;
    month: ApiClmmPoolsItemStatistics;
    lookupTableAccount: string | undefined;
}
interface ApiClmmPools {
    data: ApiClmmPoolsItem[];
}
interface ApiClmmPositionLinePoint {
    price: number;
    liquidity: number;
}
interface ApiClmmPositionLine {
    data: ApiClmmPositionLinePoint[];
}

type ProgramId = {
    SERUM_MARKET: PublicKey;
    OPENBOOK_MARKET: PublicKey;
    UTIL1216: PublicKey;
    FarmV3: PublicKey;
    FarmV5: PublicKey;
    FarmV6: PublicKey;
    AmmV4: PublicKey;
    AmmStable: PublicKey;
    CLMM: PublicKey;
    Router: PublicKey;
};
declare const MAINNET_PROGRAM_ID: ProgramId;
declare const DEVNET_PROGRAM_ID: ProgramId;

declare const TICK_ARRAY_SIZE = 60;
declare const TICK_ARRAY_BITMAP_SIZE = 512;
type Tick = {
    tick: number;
    liquidityNet: BN__default;
    liquidityGross: BN__default;
    feeGrowthOutsideX64A: BN__default;
    feeGrowthOutsideX64B: BN__default;
    rewardGrowthsOutsideX64: BN__default[];
};
type TickArray = {
    address: PublicKey;
    poolId: PublicKey;
    startTickIndex: number;
    ticks: Tick[];
    initializedTickCount: number;
};
type TickState = {
    tick: number;
    liquidityNet: BN__default;
    liquidityGross: BN__default;
    feeGrowthOutsideX64A: BN__default;
    feeGrowthOutsideX64B: BN__default;
    tickCumulativeOutside: BN__default;
    secondsPerLiquidityOutsideX64: BN__default;
    secondsOutside: number;
    rewardGrowthsOutside: BN__default[];
};
type TickArrayState = {
    ammPool: PublicKey;
    startTickIndex: number;
    ticks: TickState[];
    initializedTickCount: number;
};
declare class TickUtils {
    static getTickArrayAddressByTick(programId: PublicKey, poolId: PublicKey, tickIndex: number, tickSpacing: number): PublicKey;
    static getTickOffsetInArray(tickIndex: number, tickSpacing: number): number;
    static getTickArrayBitIndex(tickIndex: number, tickSpacing: number): number;
    static getTickArrayStartIndexByTick(tickIndex: number, tickSpacing: number): number;
    static getTickArrayOffsetInBitmapByTick(tick: number, tickSpacing: number): number;
    static checkTickArrayIsInitialized(bitmap: BN__default, tick: number, tickSpacing: number): {
        isInitialized: boolean;
        startIndex: number;
    };
    static getNextTickArrayStartIndex(lastTickArrayStartIndex: number, tickSpacing: number, zeroForOne: boolean): number;
    static mergeTickArrayBitmap(bns: BN__default[]): BN__default;
    static getInitializedTickArrayInRange(tickArrayBitmap: BN__default[], exTickArrayBitmap: TickArrayBitmapExtensionType, tickSpacing: number, tickArrayStartIndex: number, expectedCount: number): number[];
    static getAllInitializedTickArrayStartIndex(tickArrayBitmap: BN__default[], exTickArrayBitmap: TickArrayBitmapExtensionType, tickSpacing: number): number[];
    static getAllInitializedTickArrayInfo(programId: PublicKey, poolId: PublicKey, tickArrayBitmap: BN__default[], exTickArrayBitmap: TickArrayBitmapExtensionType, tickSpacing: number): {
        tickArrayStartIndex: number;
        tickArrayAddress: PublicKey;
    }[];
    static getAllInitializedTickInTickArray(tickArray: TickArrayState): TickState[];
    static searchLowBitFromStart(tickArrayBitmap: BN__default[], exTickArrayBitmap: TickArrayBitmapExtensionType, currentTickArrayBitStartIndex: number, expectedCount: number, tickSpacing: number): number[];
    static searchHightBitFromStart(tickArrayBitmap: BN__default[], exTickArrayBitmap: TickArrayBitmapExtensionType, currentTickArrayBitStartIndex: number, expectedCount: number, tickSpacing: number): number[];
    static checkIsOutOfBoundary(tick: number): boolean;
    static nextInitTick(tickArrayCurrent: TickArray, currentTickIndex: number, tickSpacing: number, zeroForOne: boolean, t: boolean): Tick | null;
    static firstInitializedTick(tickArrayCurrent: TickArray, zeroForOne: boolean): Tick;
}

interface ClmmConfigInfo {
    id: PublicKey;
    index: number;
    protocolFeeRate: number;
    tradeFeeRate: number;
    tickSpacing: number;
    fundFeeRate: number;
    fundOwner: string;
    description: string;
}
interface ClmmPoolRewardLayoutInfo {
    rewardState: number;
    openTime: BN__default;
    endTime: BN__default;
    lastUpdateTime: BN__default;
    emissionsPerSecondX64: BN__default;
    rewardTotalEmissioned: BN__default;
    rewardClaimed: BN__default;
    tokenMint: PublicKey;
    tokenVault: PublicKey;
    creator: PublicKey;
    rewardGrowthGlobalX64: BN__default;
}
interface ClmmPoolRewardInfo {
    rewardState: number;
    openTime: BN__default;
    endTime: BN__default;
    lastUpdateTime: BN__default;
    emissionsPerSecondX64: BN__default;
    rewardTotalEmissioned: BN__default;
    rewardClaimed: BN__default;
    tokenProgramId: PublicKey;
    tokenMint: PublicKey;
    tokenVault: PublicKey;
    creator: PublicKey;
    rewardGrowthGlobalX64: BN__default;
    perSecond: Decimal$1;
    remainingRewards: undefined | BN__default;
}
interface ClmmPoolInfo {
    id: PublicKey;
    mintA: {
        programId: PublicKey;
        mint: PublicKey;
        vault: PublicKey;
        decimals: number;
    };
    mintB: {
        programId: PublicKey;
        mint: PublicKey;
        vault: PublicKey;
        decimals: number;
    };
    ammConfig: ClmmConfigInfo;
    observationId: PublicKey;
    creator: PublicKey;
    programId: PublicKey;
    version: 6;
    tickSpacing: number;
    liquidity: BN__default;
    sqrtPriceX64: BN__default;
    currentPrice: Decimal$1;
    tickCurrent: number;
    observationIndex: number;
    observationUpdateDuration: number;
    feeGrowthGlobalX64A: BN__default;
    feeGrowthGlobalX64B: BN__default;
    protocolFeesTokenA: BN__default;
    protocolFeesTokenB: BN__default;
    swapInAmountTokenA: BN__default;
    swapOutAmountTokenB: BN__default;
    swapInAmountTokenB: BN__default;
    swapOutAmountTokenA: BN__default;
    tickArrayBitmap: BN__default[];
    rewardInfos: ClmmPoolRewardInfo[];
    day: ApiClmmPoolsItemStatistics;
    week: ApiClmmPoolsItemStatistics;
    month: ApiClmmPoolsItemStatistics;
    tvl: number;
    lookupTableAccount: PublicKey;
    startTime: number;
    exBitmapInfo: TickArrayBitmapExtensionType;
}
interface ClmmPoolPersonalPosition {
    poolId: PublicKey;
    nftMint: PublicKey;
    priceLower: Decimal$1;
    priceUpper: Decimal$1;
    amountA: BN__default;
    amountB: BN__default;
    tickLower: number;
    tickUpper: number;
    liquidity: BN__default;
    feeGrowthInsideLastX64A: BN__default;
    feeGrowthInsideLastX64B: BN__default;
    tokenFeesOwedA: BN__default;
    tokenFeesOwedB: BN__default;
    rewardInfos: {
        growthInsideLastX64: BN__default;
        rewardAmountOwed: BN__default;
        pendingReward: BN__default;
    }[];
    leverage: number;
    tokenFeeAmountA: BN__default;
    tokenFeeAmountB: BN__default;
}
interface MintInfo {
    mint: PublicKey;
    decimals: number;
    programId: PublicKey;
}
interface ReturnTypeGetLiquidityAmountOut {
    liquidity: BN__default;
    amountSlippageA: GetTransferAmountFee;
    amountSlippageB: GetTransferAmountFee;
    amountA: GetTransferAmountFee;
    amountB: GetTransferAmountFee;
    expirationTime: number | undefined;
}
interface ReturnTypeGetPriceAndTick {
    tick: number;
    price: Decimal$1;
}
interface ReturnTypeGetTickPrice {
    tick: number;
    price: Decimal$1;
    tickSqrtPriceX64: BN__default;
}
interface ReturnTypeComputeAmountOutFormat {
    allTrade: boolean;
    realAmountIn: TransferAmountFee;
    amountOut: TransferAmountFee;
    minAmountOut: TransferAmountFee;
    expirationTime: number | undefined;
    currentPrice: Price;
    executionPrice: Price;
    priceImpact: Percent;
    fee: CurrencyAmount;
    remainingAccounts: PublicKey[];
    executionPriceX64: BN__default;
}
interface ReturnTypeComputeAmountOut {
    allTrade: boolean;
    realAmountIn: GetTransferAmountFee;
    amountOut: GetTransferAmountFee;
    minAmountOut: GetTransferAmountFee;
    expirationTime: number | undefined;
    currentPrice: Decimal$1;
    executionPrice: Decimal$1;
    priceImpact: Percent;
    fee: BN__default;
    remainingAccounts: PublicKey[];
    executionPriceX64: BN__default;
}
interface ReturnTypeComputeAmountOutBaseOut {
    amountIn: GetTransferAmountFee;
    maxAmountIn: GetTransferAmountFee;
    realAmountOut: GetTransferAmountFee;
    expirationTime: number | undefined;
    currentPrice: Decimal$1;
    executionPrice: Decimal$1;
    priceImpact: Percent;
    fee: BN__default;
    remainingAccounts: PublicKey[];
}
interface ReturnTypeFetchMultiplePoolInfos {
    [id: string]: {
        state: ClmmPoolInfo;
        positionAccount?: ClmmPoolPersonalPosition[] | undefined;
    };
}
interface ReturnTypeFetchMultiplePoolTickArrays {
    [poolId: string]: {
        [key: string]: TickArray;
    };
}
interface TickArrayBitmapExtensionType {
    poolId: PublicKey;
    positiveTickArrayBitmap: BN__default[][];
    negativeTickArrayBitmap: BN__default[][];
}
interface ReturnTypeFetchExBitmaps {
    [exBitmapId: string]: TickArrayBitmapExtensionType;
}
declare class Clmm extends Base {
    static makeMockPoolInfo({ programId, mint1, mint2, ammConfig, createPoolInstructionSimpleAddress, initialPrice, startTime, owner, }: {
        programId: PublicKey;
        mint1: MintInfo;
        mint2: MintInfo;
        ammConfig: ClmmConfigInfo;
        createPoolInstructionSimpleAddress: {
            observationId: PublicKey;
            poolId: PublicKey;
            mintAVault: PublicKey;
            mintBVault: PublicKey;
            mintA: PublicKey;
            mintB: PublicKey;
            mintProgramIdA: PublicKey;
            mintProgramIdB: PublicKey;
        };
        initialPrice: Decimal$1;
        startTime: BN__default;
        owner: PublicKey;
    }): ClmmPoolInfo;
    static makeCreatePoolInstructionSimple<T extends TxVersion>({ makeTxVersion, connection, programId, owner, payer, mint1, mint2, ammConfig, initialPrice, startTime, computeBudgetConfig, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        programId: PublicKey;
        owner: PublicKey;
        payer: PublicKey;
        mint1: MintInfo;
        mint2: MintInfo;
        ammConfig: ClmmConfigInfo;
        initialPrice: Decimal$1;
        startTime: BN__default;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            mintA: PublicKey;
            mintB: PublicKey;
            mintProgramIdA: PublicKey;
            mintProgramIdB: PublicKey;
            observationId: PublicKey;
            poolId: PublicKey;
            mintAVault: PublicKey;
            mintBVault: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeOpenPositionFromLiquidityInstructionSimple<T extends TxVersion>({ makeTxVersion, connection, poolInfo, ownerInfo, amountMaxA, amountMaxB, tickLower, tickUpper, liquidity, associatedOnly, checkCreateATAOwner, withMetadata, getEphemeralSigners, computeBudgetConfig, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        amountMaxA: BN__default;
        amountMaxB: BN__default;
        tickLower: number;
        tickUpper: number;
        withMetadata?: 'create' | 'no-create';
        liquidity: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
        getEphemeralSigners?: (k: number) => any;
    }): Promise<{
        address: {
            nftMint: PublicKey;
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            metadataAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeOpenPositionFromBaseInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, tickLower, tickUpper, base, baseAmount, otherAmountMax, associatedOnly, checkCreateATAOwner, computeBudgetConfig, withMetadata, makeTxVersion, lookupTableCache, getEphemeralSigners, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        tickLower: number;
        tickUpper: number;
        withMetadata?: 'create' | 'no-create';
        base: 'MintA' | 'MintB';
        baseAmount: BN__default;
        otherAmountMax: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
        getEphemeralSigners?: (k: number) => any;
    }): Promise<{
        address: {
            nftMint: PublicKey;
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            metadataAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeIncreasePositionFromLiquidityInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerPosition, ownerInfo, amountMaxA, amountMaxB, liquidity, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        amountMaxA: BN__default;
        amountMaxB: BN__default;
        liquidity: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeIncreasePositionFromBaseInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerPosition, ownerInfo, base, baseAmount, otherAmountMax, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        base: 'MintA' | 'MintB';
        baseAmount: BN__default;
        otherAmountMax: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeDecreaseLiquidityInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerPosition, ownerInfo, liquidity, amountMinA, amountMinB, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
            closePosition?: boolean;
        };
        liquidity: BN__default;
        amountMinA: BN__default;
        amountMinB: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeSwapBaseInInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, inputMint, amountIn, amountOutMin, priceLimit, remainingAccounts, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        inputMint: PublicKey;
        amountIn: BN__default;
        amountOutMin: BN__default;
        priceLimit?: Decimal$1;
        remainingAccounts: PublicKey[];
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeSwapBaseOutInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, outputMint, amountOut, amountInMax, priceLimit, remainingAccounts, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        outputMint: PublicKey;
        amountOut: BN__default;
        amountInMax: BN__default;
        priceLimit?: Decimal$1;
        remainingAccounts: PublicKey[];
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCLosePositionInstructionSimple<T extends TxVersion>({ poolInfo, ownerPosition, ownerInfo, makeTxVersion, lookupTableCache, connection, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            wallet: PublicKey;
            feePayer: PublicKey;
        };
    }): Promise<{
        address: {
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeInitRewardInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, rewardInfo, chainTime, associatedOnly, checkCreateATAOwner, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        rewardInfo: {
            programId: PublicKey;
            mint: PublicKey;
            openTime: number;
            endTime: number;
            perSecond: Decimal$1;
        };
        chainTime: number;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
    }): Promise<{
        address: {
            poolRewardVault: PublicKey;
            operationId: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeInitRewardsInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, rewardInfos, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        rewardInfos: {
            programId: PublicKey;
            mint: PublicKey;
            openTime: number;
            endTime: number;
            perSecond: Decimal$1;
        }[];
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeSetRewardInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, rewardInfo, chainTime, associatedOnly, checkCreateATAOwner, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        rewardInfo: {
            programId: PublicKey;
            mint: PublicKey;
            openTime: number;
            endTime: number;
            perSecond: Decimal$1;
        };
        chainTime: number;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
    }): Promise<{
        address: {
            rewardVault: PublicKey;
            operationId: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeSetRewardsInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, rewardInfos, chainTime, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        rewardInfos: {
            programId: PublicKey;
            mint: PublicKey;
            openTime: number;
            endTime: number;
            perSecond: Decimal$1;
        }[];
        chainTime: number;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCollectRewardInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, rewardMint, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        rewardMint: PublicKey;
        associatedOnly: boolean;
        checkCreateATAOwner: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            rewardVault: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCollectRewardsInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, rewardMints, associatedOnly, checkCreateATAOwner, computeBudgetConfig, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        rewardMints: PublicKey[];
        associatedOnly: boolean;
        checkCreateATAOwner: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeHarvestAllRewardInstructionSimple<T extends TxVersion>({ connection, fetchPoolInfos, ownerInfo, associatedOnly, checkCreateATAOwner, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        fetchPoolInfos: ReturnTypeFetchMultiplePoolInfos;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCreatePoolInstructions({ connection, programId, owner, mintA, mintB, ammConfigId, initialPriceX64, startTime, }: {
        connection: Connection;
        programId: PublicKey;
        owner: PublicKey;
        mintA: MintInfo;
        mintB: MintInfo;
        ammConfigId: PublicKey;
        initialPriceX64: BN__default;
        startTime: BN__default;
    }): Promise<{
        address: {
            observationId: PublicKey;
            poolId: PublicKey;
            mintAVault: PublicKey;
            mintBVault: PublicKey;
        };
        innerTransaction: InnerTransaction;
    }>;
    static makeOpenPositionFromLiquidityInstructions({ poolInfo, ownerInfo, tickLower, tickUpper, liquidity, amountMaxA, amountMaxB, withMetadata, getEphemeralSigners, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
        };
        tickLower: number;
        tickUpper: number;
        liquidity: BN__default;
        amountMaxA: BN__default;
        amountMaxB: BN__default;
        withMetadata: 'create' | 'no-create';
        getEphemeralSigners?: (k: number) => any;
    }): Promise<{
        address: {
            nftMint: PublicKey;
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            metadataAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransaction: InnerTransaction;
    }>;
    static makeOpenPositionFromBaseInstructions({ poolInfo, ownerInfo, tickLower, tickUpper, base, baseAmount, otherAmountMax, withMetadata, getEphemeralSigners, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
        };
        tickLower: number;
        tickUpper: number;
        withMetadata: 'create' | 'no-create';
        base: 'MintA' | 'MintB';
        baseAmount: BN__default;
        otherAmountMax: BN__default;
        getEphemeralSigners?: (k: number) => any;
    }): Promise<{
        address: {
            nftMint: PublicKey;
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            metadataAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransaction: InnerTransaction;
    }>;
    static makeIncreasePositionFromLiquidityInstructions({ poolInfo, ownerPosition, ownerInfo, liquidity, amountMaxA, amountMaxB, }: {
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
        };
        liquidity: BN__default;
        amountMaxA: BN__default;
        amountMaxB: BN__default;
    }): {
        address: {
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static makeIncreasePositionFromBaseInstructions({ poolInfo, ownerPosition, ownerInfo, base, baseAmount, otherAmountMax, }: {
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
        };
        base: 'MintA' | 'MintB';
        baseAmount: BN__default;
        otherAmountMax: BN__default;
    }): {
        address: {
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static makeDecreaseLiquidityInstructions({ poolInfo, ownerPosition, ownerInfo, liquidity, amountMinA, amountMinB, }: {
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
            rewardAccounts: PublicKey[];
        };
        liquidity: BN__default;
        amountMinA: BN__default;
        amountMinB: BN__default;
    }): {
        address: {
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static makeClosePositionInstructions({ poolInfo, ownerInfo, ownerPosition, }: {
        poolInfo: ClmmPoolInfo;
        ownerPosition: ClmmPoolPersonalPosition;
        ownerInfo: {
            wallet: PublicKey;
        };
    }): {
        address: {
            positionNftAccount: PublicKey;
            personalPosition: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static makeSwapBaseInInstructions({ poolInfo, ownerInfo, inputMint, amountIn, amountOutMin, sqrtPriceLimitX64, remainingAccounts, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
        };
        inputMint: PublicKey;
        amountIn: BN__default;
        amountOutMin: BN__default;
        sqrtPriceLimitX64: BN__default;
        remainingAccounts: PublicKey[];
    }): {
        address: {};
        innerTransaction: InnerTransaction;
    };
    static makeSwapBaseOutInstructions({ poolInfo, ownerInfo, outputMint, amountOut, amountInMax, sqrtPriceLimitX64, remainingAccounts, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccountA: PublicKey;
            tokenAccountB: PublicKey;
        };
        outputMint: PublicKey;
        amountOut: BN__default;
        amountInMax: BN__default;
        sqrtPriceLimitX64: BN__default;
        remainingAccounts: PublicKey[];
    }): {
        address: {};
        innerTransaction: InnerTransaction;
    };
    static makeInitRewardInstructions({ poolInfo, ownerInfo, rewardInfo, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccount: PublicKey;
        };
        rewardInfo: {
            programId: PublicKey;
            mint: PublicKey;
            openTime: number;
            endTime: number;
            emissionsPerSecondX64: BN__default;
        };
    }): {
        address: {
            poolRewardVault: PublicKey;
            operationId: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static makeSetRewardInstructions({ poolInfo, ownerInfo, rewardInfo, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccount: PublicKey;
        };
        rewardInfo: {
            mint: PublicKey;
            openTime: number;
            endTime: number;
            emissionsPerSecondX64: BN__default;
        };
    }): {
        address: {
            rewardVault: PublicKey;
            operationId: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static makeCollectRewardInstructions({ poolInfo, ownerInfo, rewardMint, }: {
        poolInfo: ClmmPoolInfo;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccount: PublicKey;
        };
        rewardMint: PublicKey;
    }): {
        address: {
            rewardVault: PublicKey;
        };
        innerTransaction: InnerTransaction;
    };
    static getLiquidityAmountOutFromAmountIn({ poolInfo, inputA, tickLower, tickUpper, amount, slippage, add, token2022Infos, epochInfo, amountHasFee, }: {
        poolInfo: ClmmPoolInfo;
        inputA: boolean;
        tickLower: number;
        tickUpper: number;
        amount: BN__default;
        slippage: number;
        add: boolean;
        amountHasFee: boolean;
        token2022Infos: ReturnTypeFetchMultipleMintInfos;
        epochInfo: EpochInfo;
    }): ReturnTypeGetLiquidityAmountOut;
    static getLiquidityFromAmounts({ poolInfo, tickLower, tickUpper, amountA, amountB, slippage, add, token2022Infos, epochInfo, amountHasFee, }: {
        poolInfo: ClmmPoolInfo;
        tickLower: number;
        tickUpper: number;
        amountA: BN__default;
        amountB: BN__default;
        slippage: number;
        add: boolean;
        token2022Infos: ReturnTypeFetchMultipleMintInfos;
        epochInfo: EpochInfo;
        amountHasFee: boolean;
    }): ReturnTypeGetLiquidityAmountOut;
    static getAmountsFromLiquidity({ poolInfo, tickLower, tickUpper, liquidity, slippage, add, token2022Infos, epochInfo, amountAddFee, }: {
        poolInfo: ClmmPoolInfo;
        tickLower: number;
        tickUpper: number;
        liquidity: BN__default;
        slippage: number;
        add: boolean;
        token2022Infos: ReturnTypeFetchMultipleMintInfos;
        epochInfo: EpochInfo;
        amountAddFee: boolean;
    }): ReturnTypeGetLiquidityAmountOut;
    static getPriceAndTick({ poolInfo, price, baseIn, }: {
        poolInfo: ClmmPoolInfo;
        price: Decimal$1;
        baseIn: boolean;
    }): ReturnTypeGetPriceAndTick;
    static getTickPrice({ poolInfo, tick, baseIn, }: {
        poolInfo: ClmmPoolInfo;
        tick: number;
        baseIn: boolean;
    }): ReturnTypeGetTickPrice;
    static computeAmountOutFormat({ poolInfo, tickArrayCache, token2022Infos, epochInfo, amountIn, currencyOut, slippage, catchLiquidityInsufficient, }: {
        poolInfo: ClmmPoolInfo;
        tickArrayCache: {
            [key: string]: TickArray;
        };
        token2022Infos: ReturnTypeFetchMultipleMintInfos;
        epochInfo: EpochInfo;
        amountIn: CurrencyAmount | TokenAmount;
        currencyOut: Token | Currency;
        slippage: Percent;
        catchLiquidityInsufficient: boolean;
    }): ReturnTypeComputeAmountOutFormat;
    static computeAmountOutAndCheckToken({ connection, poolInfo, tickArrayCache, baseMint, amountIn, slippage, priceLimit, catchLiquidityInsufficient, }: {
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        tickArrayCache: {
            [key: string]: TickArray;
        };
        baseMint: PublicKey;
        amountIn: BN__default;
        slippage: number;
        priceLimit?: Decimal$1;
        catchLiquidityInsufficient: boolean;
    }): Promise<ReturnTypeComputeAmountOut>;
    static computeAmountOut({ poolInfo, tickArrayCache, baseMint, token2022Infos, epochInfo, amountIn, slippage, priceLimit, catchLiquidityInsufficient, }: {
        poolInfo: ClmmPoolInfo;
        tickArrayCache: {
            [key: string]: TickArray;
        };
        baseMint: PublicKey;
        token2022Infos: ReturnTypeFetchMultipleMintInfos;
        epochInfo: EpochInfo;
        amountIn: BN__default;
        slippage: number;
        priceLimit?: Decimal$1;
        catchLiquidityInsufficient: boolean;
    }): ReturnTypeComputeAmountOut;
    static computeAmountInAndCheckToken({ connection, poolInfo, tickArrayCache, baseMint, amountOut, slippage, priceLimit, }: {
        connection: Connection;
        poolInfo: ClmmPoolInfo;
        tickArrayCache: {
            [key: string]: TickArray;
        };
        baseMint: PublicKey;
        amountOut: BN__default;
        slippage: number;
        priceLimit?: Decimal$1;
    }): Promise<ReturnTypeComputeAmountOutBaseOut>;
    static computeAmountIn({ poolInfo, tickArrayCache, baseMint, token2022Infos, epochInfo, amountOut, slippage, priceLimit, }: {
        poolInfo: ClmmPoolInfo;
        tickArrayCache: {
            [key: string]: TickArray;
        };
        baseMint: PublicKey;
        token2022Infos: ReturnTypeFetchMultipleMintInfos;
        epochInfo: EpochInfo;
        amountOut: BN__default;
        slippage: number;
        priceLimit?: Decimal$1;
    }): ReturnTypeComputeAmountOutBaseOut;
    static estimateAprsForPriceRangeMultiplier({ poolInfo, aprType, positionTickLowerIndex, positionTickUpperIndex, }: {
        poolInfo: ClmmPoolInfo;
        aprType: 'day' | 'week' | 'month';
        positionTickLowerIndex: number;
        positionTickUpperIndex: number;
    }): {
        feeApr: number;
        rewardsApr: number[];
        apr: number;
    };
    static estimateAprsForPriceRangeDelta({ poolInfo, aprType, mintPrice, rewardMintDecimals, liquidity, positionTickLowerIndex, positionTickUpperIndex, chainTime, }: {
        poolInfo: ClmmPoolInfo;
        aprType: 'day' | 'week' | 'month';
        mintPrice: {
            [mint: string]: Price;
        };
        rewardMintDecimals: {
            [mint: string]: number;
        };
        liquidity: BN__default;
        positionTickLowerIndex: number;
        positionTickUpperIndex: number;
        chainTime: number;
    }): {
        feeApr: number;
        rewardsApr: number[];
        apr: number;
    };
    static fetchMultiplePoolInfos({ connection, poolKeys, ownerInfo, chainTime, batchRequest, updateOwnerRewardAndFee, }: {
        connection: Connection;
        poolKeys: ApiClmmPoolsItem[];
        ownerInfo?: {
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
        };
        chainTime: number;
        batchRequest?: boolean;
        updateOwnerRewardAndFee?: boolean;
    }): Promise<ReturnTypeFetchMultiplePoolInfos>;
    static fetchMultiplePoolTickArrays({ connection, poolKeys, batchRequest, }: {
        connection: Connection;
        poolKeys: ClmmPoolInfo[];
        batchRequest?: boolean;
    }): Promise<ReturnTypeFetchMultiplePoolTickArrays>;
    static fetchExBitmaps({ connection, exBitmapAddress, batchRequest, }: {
        connection: Connection;
        exBitmapAddress: PublicKey[];
        batchRequest: boolean;
    }): Promise<ReturnTypeFetchExBitmaps>;
    static getWhiteListMint({ connection, programId }: {
        connection: Connection;
        programId: PublicKey;
    }): Promise<PublicKey[]>;
}

declare function createPoolInstruction(programId: PublicKey, poolId: PublicKey, poolCreator: PublicKey, ammConfigId: PublicKey, observationId: PublicKey, mintA: PublicKey, mintVaultA: PublicKey, mintProgramIdA: PublicKey, mintB: PublicKey, mintVaultB: PublicKey, mintProgramIdB: PublicKey, exTickArrayBitmap: PublicKey, sqrtPriceX64: BN__default, startTime: BN__default): TransactionInstruction;
declare function openPositionFromLiquidityInstruction(programId: PublicKey, payer: PublicKey, poolId: PublicKey, positionNftOwner: PublicKey, positionNftMint: PublicKey, positionNftAccount: PublicKey, metadataAccount: PublicKey, protocolPosition: PublicKey, tickArrayLower: PublicKey, tickArrayUpper: PublicKey, personalPosition: PublicKey, ownerTokenAccountA: PublicKey, ownerTokenAccountB: PublicKey, tokenVaultA: PublicKey, tokenVaultB: PublicKey, tokenMintA: PublicKey, tokenMintB: PublicKey, tickLowerIndex: number, tickUpperIndex: number, tickArrayLowerStartIndex: number, tickArrayUpperStartIndex: number, liquidity: BN__default, amountMaxA: BN__default, amountMaxB: BN__default, withMetadata: 'create' | 'no-create', exTickArrayBitmap?: PublicKey): TransactionInstruction;
declare function openPositionFromBaseInstruction(programId: PublicKey, payer: PublicKey, poolId: PublicKey, positionNftOwner: PublicKey, positionNftMint: PublicKey, positionNftAccount: PublicKey, metadataAccount: PublicKey, protocolPosition: PublicKey, tickArrayLower: PublicKey, tickArrayUpper: PublicKey, personalPosition: PublicKey, ownerTokenAccountA: PublicKey, ownerTokenAccountB: PublicKey, tokenVaultA: PublicKey, tokenVaultB: PublicKey, tokenMintA: PublicKey, tokenMintB: PublicKey, tickLowerIndex: number, tickUpperIndex: number, tickArrayLowerStartIndex: number, tickArrayUpperStartIndex: number, withMetadata: 'create' | 'no-create', base: 'MintA' | 'MintB', baseAmount: BN__default, otherAmountMax: BN__default, exTickArrayBitmap?: PublicKey): TransactionInstruction;
declare function closePositionInstruction(programId: PublicKey, positionNftOwner: PublicKey, positionNftMint: PublicKey, positionNftAccount: PublicKey, personalPosition: PublicKey): TransactionInstruction;
declare function increasePositionFromLiquidityInstruction(programId: PublicKey, positionNftOwner: PublicKey, positionNftAccount: PublicKey, personalPosition: PublicKey, poolId: PublicKey, protocolPosition: PublicKey, tickArrayLower: PublicKey, tickArrayUpper: PublicKey, ownerTokenAccountA: PublicKey, ownerTokenAccountB: PublicKey, mintVaultA: PublicKey, mintVaultB: PublicKey, mintMintA: PublicKey, mintMintB: PublicKey, liquidity: BN__default, amountMaxA: BN__default, amountMaxB: BN__default, exTickArrayBitmap?: PublicKey): TransactionInstruction;
declare function increasePositionFromBaseInstruction(programId: PublicKey, positionNftOwner: PublicKey, positionNftAccount: PublicKey, personalPosition: PublicKey, poolId: PublicKey, protocolPosition: PublicKey, tickArrayLower: PublicKey, tickArrayUpper: PublicKey, ownerTokenAccountA: PublicKey, ownerTokenAccountB: PublicKey, mintVaultA: PublicKey, mintVaultB: PublicKey, mintMintA: PublicKey, mintMintB: PublicKey, base: 'MintA' | 'MintB', baseAmount: BN__default, otherAmountMax: BN__default, exTickArrayBitmap?: PublicKey): TransactionInstruction;
declare function decreaseLiquidityInstruction(programId: PublicKey, positionNftOwner: PublicKey, positionNftAccount: PublicKey, personalPosition: PublicKey, poolId: PublicKey, protocolPosition: PublicKey, tickArrayLower: PublicKey, tickArrayUpper: PublicKey, ownerTokenAccountA: PublicKey, ownerTokenAccountB: PublicKey, mintVaultA: PublicKey, mintVaultB: PublicKey, mintMintA: PublicKey, mintMintB: PublicKey, rewardAccounts: {
    poolRewardVault: PublicKey;
    ownerRewardVault: PublicKey;
    rewardMint: PublicKey;
}[], liquidity: BN__default, amountMinA: BN__default, amountMinB: BN__default, exTickArrayBitmap?: PublicKey): TransactionInstruction;
declare function swapInstruction(programId: PublicKey, payer: PublicKey, poolId: PublicKey, ammConfigId: PublicKey, inputTokenAccount: PublicKey, outputTokenAccount: PublicKey, inputVault: PublicKey, outputVault: PublicKey, inputMint: PublicKey, outputMint: PublicKey, tickArray: PublicKey[], observationId: PublicKey, amount: BN__default, otherAmountThreshold: BN__default, sqrtPriceLimitX64: BN__default, isBaseInput: boolean, exTickArrayBitmap?: PublicKey): TransactionInstruction;
declare function initRewardInstruction(programId: PublicKey, payer: PublicKey, poolId: PublicKey, operationId: PublicKey, ammConfigId: PublicKey, ownerTokenAccount: PublicKey, rewardProgramId: PublicKey, rewardMint: PublicKey, rewardVault: PublicKey, openTime: number, endTime: number, emissionsPerSecondX64: BN__default): TransactionInstruction;
declare function setRewardInstruction(programId: PublicKey, payer: PublicKey, poolId: PublicKey, operationId: PublicKey, ammConfigId: PublicKey, ownerTokenAccount: PublicKey, rewardVault: PublicKey, rewardMint: PublicKey, rewardIndex: number, openTime: number, endTime: number, emissionsPerSecondX64: BN__default): TransactionInstruction;
declare function collectRewardInstruction(programId: PublicKey, payer: PublicKey, poolId: PublicKey, ownerTokenAccount: PublicKey, rewardVault: PublicKey, rewardMint: PublicKey, rewardIndex: number): TransactionInstruction;

declare const AmmConfigLayout: Structure<number | number[] | _solana_web3_js.PublicKey | Buffer | BN[], "", {
    padding: number[] | BN[];
    bump: number;
    index: number;
    protocolFeeRate: number;
    tradeFeeRate: number;
    tickSpacing: number;
    fundFeeRate: number;
    fundOwner: _solana_web3_js.PublicKey;
}>;
declare const ObservationLayout: Structure<number | BN | BN[], "", {
    sqrtPriceX64: BN;
    blockTimestamp: number;
    cumulativeTimePriceX64: BN;
}>;
declare const ObservationInfoLayout: Structure<boolean | _solana_web3_js.PublicKey | Buffer | BN[] | {
    sqrtPriceX64: BN;
    blockTimestamp: number;
    cumulativeTimePriceX64: BN;
}[], "", {
    poolId: _solana_web3_js.PublicKey;
    initialized: boolean;
    observations: {
        sqrtPriceX64: BN;
        blockTimestamp: number;
        cumulativeTimePriceX64: BN;
    }[];
}>;
declare const RewardInfo: Structure<number | _solana_web3_js.PublicKey | BN, "", {
    openTime: BN;
    endTime: BN;
    emissionsPerSecondX64: BN;
    rewardState: number;
    lastUpdateTime: BN;
    rewardTotalEmissioned: BN;
    rewardClaimed: BN;
    tokenMint: _solana_web3_js.PublicKey;
    tokenVault: _solana_web3_js.PublicKey;
    creator: _solana_web3_js.PublicKey;
    rewardGrowthGlobalX64: BN;
}>;
declare const PoolInfoLayout: Structure<number | number[] | _solana_web3_js.PublicKey | Buffer | BN | BN[] | {
    openTime: BN;
    endTime: BN;
    emissionsPerSecondX64: BN;
    rewardState: number;
    lastUpdateTime: BN;
    rewardTotalEmissioned: BN;
    rewardClaimed: BN;
    tokenMint: _solana_web3_js.PublicKey;
    tokenVault: _solana_web3_js.PublicKey;
    creator: _solana_web3_js.PublicKey;
    rewardGrowthGlobalX64: BN;
}[], "", {
    status: number;
    padding: BN[];
    sqrtPriceX64: BN;
    startTime: BN;
    liquidity: BN;
    bump: number;
    tickSpacing: number;
    creator: _solana_web3_js.PublicKey;
    ammConfig: _solana_web3_js.PublicKey;
    mintA: _solana_web3_js.PublicKey;
    mintB: _solana_web3_js.PublicKey;
    vaultA: _solana_web3_js.PublicKey;
    vaultB: _solana_web3_js.PublicKey;
    observationId: _solana_web3_js.PublicKey;
    mintDecimalsA: number;
    mintDecimalsB: number;
    tickCurrent: number;
    observationIndex: number;
    observationUpdateDuration: number;
    feeGrowthGlobalX64A: BN;
    feeGrowthGlobalX64B: BN;
    protocolFeesTokenA: BN;
    protocolFeesTokenB: BN;
    swapInAmountTokenA: BN;
    swapOutAmountTokenB: BN;
    swapInAmountTokenB: BN;
    swapOutAmountTokenA: BN;
    rewardInfos: {
        openTime: BN;
        endTime: BN;
        emissionsPerSecondX64: BN;
        rewardState: number;
        lastUpdateTime: BN;
        rewardTotalEmissioned: BN;
        rewardClaimed: BN;
        tokenMint: _solana_web3_js.PublicKey;
        tokenVault: _solana_web3_js.PublicKey;
        creator: _solana_web3_js.PublicKey;
        rewardGrowthGlobalX64: BN;
    }[];
    tickArrayBitmap: BN[];
    totalFeesTokenA: BN;
    totalFeesClaimedTokenA: BN;
    totalFeesTokenB: BN;
    totalFeesClaimedTokenB: BN;
    fundFeesTokenA: BN;
    fundFeesTokenB: BN;
}>;
declare const PositionRewardInfoLayout: Structure<BN, "", {
    growthInsideLastX64: BN;
    rewardAmountOwed: BN;
}>;
declare const PositionInfoLayout: Structure<number | _solana_web3_js.PublicKey | Buffer | BN | BN[] | {
    growthInsideLastX64: BN;
    rewardAmountOwed: BN;
}[], "", {
    liquidity: BN;
    poolId: _solana_web3_js.PublicKey;
    bump: number;
    rewardInfos: {
        growthInsideLastX64: BN;
        rewardAmountOwed: BN;
    }[];
    nftMint: _solana_web3_js.PublicKey;
    tickLower: number;
    tickUpper: number;
    feeGrowthInsideLastX64A: BN;
    feeGrowthInsideLastX64B: BN;
    tokenFeesOwedA: BN;
    tokenFeesOwedB: BN;
}>;
declare const ProtocolPositionLayout: Structure<number | _solana_web3_js.PublicKey | Buffer | BN | BN[], "", {
    tickLowerIndex: number;
    tickUpperIndex: number;
    liquidity: BN;
    poolId: _solana_web3_js.PublicKey;
    bump: number;
    feeGrowthInsideLastX64A: BN;
    feeGrowthInsideLastX64B: BN;
    tokenFeesOwedA: BN;
    tokenFeesOwedB: BN;
    rewardGrowthInside: BN[];
}>;
declare const TickLayout: Structure<number | number[] | BN | BN[], "", {
    tick: number;
    liquidityNet: BN;
    liquidityGross: BN;
    feeGrowthOutsideX64A: BN;
    feeGrowthOutsideX64B: BN;
    rewardGrowthsOutsideX64: BN[];
}>;
declare const TickArrayLayout: Structure<number | number[] | _solana_web3_js.PublicKey | Buffer | {
    tick: number;
    liquidityNet: BN;
    liquidityGross: BN;
    feeGrowthOutsideX64A: BN;
    feeGrowthOutsideX64B: BN;
    rewardGrowthsOutsideX64: BN[];
}[], "", {
    poolId: _solana_web3_js.PublicKey;
    startTickIndex: number;
    ticks: {
        tick: number;
        liquidityNet: BN;
        liquidityGross: BN;
        feeGrowthOutsideX64A: BN;
        feeGrowthOutsideX64B: BN;
        rewardGrowthsOutsideX64: BN[];
    }[];
    initializedTickCount: number;
}>;
declare const OperationLayout: Structure<Buffer | _solana_web3_js.PublicKey[], "", {
    whitelistMints: _solana_web3_js.PublicKey[];
}>;
declare const TickArrayBitmapExtensionLayout: Structure<_solana_web3_js.PublicKey | Buffer | BN[][], "", {
    poolId: _solana_web3_js.PublicKey;
    positiveTickArrayBitmap: BN[][];
    negativeTickArrayBitmap: BN[][];
}>;

declare const NEGATIVE_ONE: BN__default;
declare const Q64: BN__default;
declare const Q128: BN__default;
declare const MaxU64: BN__default;
declare const U64Resolution = 64;
declare const MaxUint128: BN__default;
declare const MIN_TICK = -443636;
declare const MAX_TICK: number;
declare const MIN_SQRT_PRICE_X64: BN__default;
declare const MAX_SQRT_PRICE_X64: BN__default;
declare const BIT_PRECISION = 16;
declare const LOG_B_2_X32 = "59543866431248";
declare const LOG_B_P_ERR_MARGIN_LOWER_X64 = "184467440737095516";
declare const LOG_B_P_ERR_MARGIN_UPPER_X64 = "15793534762490258745";
declare const FEE_RATE_DENOMINATOR: BN__default;
declare enum Fee {
    rate_500 = 500,//  500 / 10e6 = 0.0005
    rate_3000 = 3000,// 3000/ 10e6 = 0.003
    rate_10000 = 10000
}
declare const TICK_SPACINGS: {
    [amount in Fee]: number;
};
declare const U64_IGNORE_RANGE: BN__default;

declare class MathUtil {
    static mulDivRoundingUp(a: BN__default, b: BN__default, denominator: BN__default): BN__default;
    static mulDivFloor(a: BN__default, b: BN__default, denominator: BN__default): BN__default;
    static mulDivCeil(a: BN__default, b: BN__default, denominator: BN__default): BN__default;
    static x64ToDecimal(num: BN__default, decimalPlaces?: number): Decimal$1;
    static decimalToX64(num: Decimal$1): BN__default;
    static wrappingSubU128(n0: BN__default, n1: BN__default): BN__default;
}
declare class SqrtPriceMath {
    static sqrtPriceX64ToPrice(sqrtPriceX64: BN__default, decimalsA: number, decimalsB: number): Decimal$1;
    static priceToSqrtPriceX64(price: Decimal$1, decimalsA: number, decimalsB: number): BN__default;
    static getNextSqrtPriceX64FromInput(sqrtPriceX64: BN__default, liquidity: BN__default, amountIn: BN__default, zeroForOne: boolean): BN__default;
    static getNextSqrtPriceX64FromOutput(sqrtPriceX64: BN__default, liquidity: BN__default, amountOut: BN__default, zeroForOne: boolean): BN__default;
    private static getNextSqrtPriceFromTokenAmountARoundingUp;
    private static getNextSqrtPriceFromTokenAmountBRoundingDown;
    static getSqrtPriceX64FromTick(tick: number): BN__default;
    static getTickFromPrice(price: Decimal$1, decimalsA: number, decimalsB: number): number;
    static getTickFromSqrtPriceX64(sqrtPriceX64: BN__default): number;
}
declare class TickMath {
    static getTickWithPriceAndTickspacing(price: Decimal$1, tickSpacing: number, mintDecimalsA: number, mintDecimalsB: number): number;
    static roundPriceWithTickspacing(price: Decimal$1, tickSpacing: number, mintDecimalsA: number, mintDecimalsB: number): Decimal$1;
}
declare class LiquidityMath {
    static addDelta(x: BN__default, y: BN__default): BN__default;
    static getTokenAmountAFromLiquidity(sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, liquidity: BN__default, roundUp: boolean): BN__default;
    static getTokenAmountBFromLiquidity(sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, liquidity: BN__default, roundUp: boolean): BN__default;
    static getLiquidityFromTokenAmountA(sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, amountA: BN__default, roundUp: boolean): BN__default;
    static getLiquidityFromTokenAmountB(sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, amountB: BN__default): BN__default;
    static getLiquidityFromTokenAmounts(sqrtPriceCurrentX64: BN__default, sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, amountA: BN__default, amountB: BN__default): BN__default;
    static getAmountsFromLiquidity(sqrtPriceCurrentX64: BN__default, sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, liquidity: BN__default, roundUp: boolean): {
        amountA: BN__default;
        amountB: BN__default;
    };
    static getAmountsFromLiquidityWithSlippage(sqrtPriceCurrentX64: BN__default, sqrtPriceX64A: BN__default, sqrtPriceX64B: BN__default, liquidity: BN__default, amountMax: boolean, roundUp: boolean, amountSlippage: number): {
        amountSlippageA: BN__default;
        amountSlippageB: BN__default;
    };
}
interface StepComputations {
    sqrtPriceStartX64: BN__default;
    tickNext: number;
    initialized: boolean;
    sqrtPriceNextX64: BN__default;
    amountIn: BN__default;
    amountOut: BN__default;
    feeAmount: BN__default;
}
declare abstract class SwapMath {
    static swapCompute(programId: PublicKey, poolId: PublicKey, tickArrayCache: {
        [key: string]: TickArray;
    }, tickArrayBitmap: BN__default[], tickarrayBitmapExtension: TickArrayBitmapExtensionType, zeroForOne: boolean, fee: number, liquidity: BN__default, currentTick: number, tickSpacing: number, currentSqrtPriceX64: BN__default, amountSpecified: BN__default, lastSavedTickArrayStartIndex: number, sqrtPriceLimitX64?: BN__default, catchLiquidityInsufficient?: boolean): {
        allTrade: boolean;
        amountSpecifiedRemaining: BN__default;
        amountCalculated: BN__default;
        feeAmount: BN__default;
        sqrtPriceX64: BN__default;
        liquidity: BN__default;
        tickCurrent: number;
        accounts: PublicKey[];
    };
    private static swapStepCompute;
}

declare const AMM_CONFIG_SEED: Buffer;
declare const POOL_SEED: Buffer;
declare const POOL_VAULT_SEED: Buffer;
declare const POOL_REWARD_VAULT_SEED: Buffer;
declare const POSITION_SEED: Buffer;
declare const TICK_ARRAY_SEED: Buffer;
declare const OPERATION_SEED: Buffer;
declare const POOL_TICK_ARRAY_BITMAP_SEED: Buffer;
declare function getPdaAmmConfigId(programId: PublicKey, index: number): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaPoolId(programId: PublicKey, ammConfigId: PublicKey, mintA: PublicKey, mintB: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaPoolVaultId(programId: PublicKey, poolId: PublicKey, vaultMint: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaPoolRewardVaulId(programId: PublicKey, poolId: PublicKey, rewardMint: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaTickArrayAddress(programId: PublicKey, poolId: PublicKey, startIndex: number): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaProtocolPositionAddress(programId: PublicKey, poolId: PublicKey, tickLower: number, tickUpper: number): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaPersonalPositionAddress(programId: PublicKey, nftMint: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaMetadataKey(mint: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaOperationAccount(programId: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};
declare function getPdaExBitmapAccount(programId: PublicKey, poolId: PublicKey): {
    publicKey: PublicKey;
    nonce: number;
};

declare class PoolUtils {
    static getOutputAmountAndRemainAccounts(poolInfo: ClmmPoolInfo, tickArrayCache: {
        [key: string]: TickArray;
    }, inputTokenMint: PublicKey, inputAmount: BN__default, sqrtPriceLimitX64?: BN__default, catchLiquidityInsufficient?: boolean): {
        allTrade: boolean;
        realTradeAmountIn: BN__default;
        expectedAmountOut: BN__default;
        remainingAccounts: PublicKey[];
        executionPrice: BN__default;
        feeAmount: BN__default;
    };
    static getInputAmountAndRemainAccounts(poolInfo: ClmmPoolInfo, tickArrayCache: {
        [key: string]: TickArray;
    }, outputTokenMint: PublicKey, outputAmount: BN__default, sqrtPriceLimitX64?: BN__default): {
        expectedAmountIn: BN__default;
        remainingAccounts: PublicKey[];
        executionPrice: BN__default;
        feeAmount: BN__default;
    };
    static getFirstInitializedTickArray(poolInfo: ClmmPoolInfo, zeroForOne: boolean): {
        isExist: true;
        startIndex: number;
        nextAccountMeta: PublicKey;
    } | {
        isExist: false;
        startIndex: undefined;
        nextAccountMeta: undefined;
    };
    static preInitializedTickArrayStartIndex(poolInfo: ClmmPoolInfo, zeroForOne: boolean): {
        isExist: boolean;
        nextStartIndex: number;
    };
    static nextInitializedTickArrayStartIndex(poolInfo: {
        tickCurrent: number;
        tickSpacing: number;
        tickArrayBitmap: BN__default[];
        exBitmapInfo: TickArrayBitmapExtensionType;
    } | ClmmPoolInfo, lastTickArrayStartIndex: number, zeroForOne: boolean): {
        isExist: boolean;
        nextStartIndex: number;
    };
    static updatePoolRewardInfos({ connection, apiPoolInfo, chainTime, poolLiquidity, rewardInfos, }: {
        connection: Connection;
        apiPoolInfo: ApiClmmPoolsItem;
        chainTime: number;
        poolLiquidity: BN__default;
        rewardInfos: ClmmPoolRewardLayoutInfo[];
    }): Promise<ClmmPoolRewardInfo[]>;
    static isOverflowDefaultTickarrayBitmap(tickSpacing: number, tickarrayStartIndexs: number[]): boolean;
    static tickRange(tickSpacing: number): {
        maxTickBoundary: number;
        minTickBoundary: number;
    };
    static get_tick_array_offset(tickarrayStartIndex: number, tickSpacing: number): number;
}

declare class PositionUtils {
    static getfeeGrowthInside(poolState: ClmmPoolInfo, tickLowerState: Tick, tickUpperState: Tick): {
        feeGrowthInsideX64A: BN__default;
        feeGrowthInsideBX64: BN__default;
    };
    static GetPositionFees(ammPool: ClmmPoolInfo, positionState: ClmmPoolPersonalPosition, tickLowerState: Tick, tickUpperState: Tick): {
        tokenFeeAmountA: BN__default;
        tokenFeeAmountB: BN__default;
    };
    static GetPositionRewards(ammPool: ClmmPoolInfo, positionState: ClmmPoolPersonalPosition, tickLowerState: Tick, tickUpperState: Tick): BN__default[];
    static getRewardGrowthInside(tickCurrentIndex: number, tickLowerState: Tick, tickUpperState: Tick, rewardInfos: ClmmPoolRewardInfo[]): BN__default[];
}

declare const FETCH_TICKARRAY_COUNT = 15;
declare type PoolVars = {
    key: PublicKey;
    tokenA: PublicKey;
    tokenB: PublicKey;
    fee: number;
};
declare class TickQuery {
    static getTickArrays(connection: Connection, programId: PublicKey, poolId: PublicKey, tickCurrent: number, tickSpacing: number, tickArrayBitmapArray: BN__default[], exTickArrayBitmap: TickArrayBitmapExtensionType): Promise<{
        [key: string]: TickArray;
    }>;
    static nextInitializedTick(programId: PublicKey, poolId: PublicKey, tickArrayCache: {
        [key: string]: TickArray;
    }, tickIndex: number, tickSpacing: number, zeroForOne: boolean): {
        nextTick: Tick;
        tickArrayAddress: PublicKey | undefined;
        tickArrayStartTickIndex: number;
    };
    static nextInitializedTickArray(tickIndex: number, tickSpacing: number, zeroForOne: boolean, tickArrayBitmap: BN__default[], exBitmapInfo: TickArrayBitmapExtensionType): {
        isExist: boolean;
        nextStartIndex: number;
    };
    static firstInitializedTickInOneArray(programId: PublicKey, poolId: PublicKey, tickArray: TickArray, zeroForOne: boolean): {
        nextTick: Tick | undefined;
        tickArrayAddress: PublicKey;
        tickArrayStartTickIndex: number;
    };
    static nextInitializedTickInOneArray(programId: PublicKey, poolId: PublicKey, tickArrayCache: {
        [key: string]: TickArray;
    }, tickIndex: number, tickSpacing: number, zeroForOne: boolean): {
        initializedTick: Tick | undefined;
        tickArrayAddress: PublicKey | undefined;
        tickArrayStartTickIndex: number;
    };
    static getArrayStartIndex(tickIndex: number, tickSpacing: number): number;
    static checkIsValidStartIndex(tickIndex: number, tickSpacing: number): boolean;
    static tickCount(tickSpacing: number): number;
}

declare const EXTENSION_TICKARRAY_BITMAP_SIZE = 14;
declare class TickArrayBitmap {
    static maxTickInTickarrayBitmap(tickSpacing: number): number;
    static getBitmapTickBoundary(tickarrayStartIndex: number, tickSpacing: number): {
        minValue: number;
        maxValue: number;
    };
    static nextInitializedTickArrayStartIndex(bitMap: BN__default, lastTickArrayStartIndex: number, tickSpacing: number, zeroForOne: boolean): {
        isInit: boolean;
        tickIndex: number;
    };
}
declare class TickArrayBitmapExtension {
    static getBitmapOffset(tickIndex: number, tickSpacing: number): number;
    static getBitmap(tickIndex: number, tickSpacing: number, tickArrayBitmapExtension: TickArrayBitmapExtensionType): {
        offset: number;
        tickarrayBitmap: BN__default[];
    };
    static checkExtensionBoundary(tickIndex: number, tickSpacing: number): void;
    static extensionTickBoundary(tickSpacing: number): {
        positiveTickBoundary: number;
        negativeTickBoundary: number;
    };
    static checkTickArrayIsInit(tickArrayStartIndex: number, tickSpacing: number, tickArrayBitmapExtension: TickArrayBitmapExtensionType): {
        isInitialized: boolean;
        startIndex: number;
    };
    static nextInitializedTickArrayFromOneBitmap(lastTickArrayStartIndex: number, tickSpacing: number, zeroForOne: boolean, tickArrayBitmapExtension: TickArrayBitmapExtensionType): {
        isInit: boolean;
        tickIndex: number;
    };
    static nextInitializedTickArrayInBitmap(tickarrayBitmap: BN__default[], nextTickArrayStartIndex: number, tickSpacing: number, zeroForOne: boolean): {
        isInit: boolean;
        tickIndex: number;
    };
    static tickArrayOffsetInBitmap(tickArrayStartIndex: number, tickSpacing: number): number;
}

declare function u16ToBytes(num: number): Uint8Array;
declare function i16ToBytes(num: number): Uint8Array;
declare function u32ToBytes(num: number): Uint8Array;
declare function i32ToBytes(num: number): Uint8Array;
declare function leadingZeros(bitNum: number, data: BN__default): number;
declare function trailingZeros(bitNum: number, data: BN__default): number;
declare function isZero(bitNum: number, data: BN__default): boolean;
declare function mostSignificantBit(bitNum: number, data: BN__default): number | null;
declare function leastSignificantBit(bitNum: number, data: BN__default): number | null;

declare function initStableModelLayout(connection: Connection): Promise<void>;
type SwapSide = 'in' | 'out';
type LiquiditySide = 'a' | 'b';
type AmountSide = 'base' | 'quote';
type LiquidityPoolKeysV4 = {
    [T in keyof ApiPoolInfoItem]: string extends ApiPoolInfoItem[T] ? PublicKey : ApiPoolInfoItem[T];
};
/**
 * Full liquidity pool keys that build transaction need
 */
type LiquidityPoolKeys = LiquidityPoolKeysV4;
interface LiquidityAssociatedPoolKeysV4 extends Omit<LiquidityPoolKeysV4, 'marketBaseVault' | 'marketQuoteVault' | 'marketBids' | 'marketAsks' | 'marketEventQueue'> {
    nonce: number;
}
/**
 * Associated liquidity pool keys
 * @remarks
 * without partial markets keys
 */
type LiquidityAssociatedPoolKeys = LiquidityAssociatedPoolKeysV4 & {
    configId: PublicKey;
};
declare enum LiquidityPoolStatus {
    Uninitialized = 0,
    Initialized = 1,
    Disabled = 2,
    RemoveLiquidityOnly = 3,
    LiquidityOnly = 4,
    OrderBook = 5,
    Swap = 6,
    WaitingForStart = 7
}
/**
 * Liquidity pool info
 * @remarks
 * same data type with layouts
 */
interface LiquidityPoolInfo {
    status: BN__default;
    baseDecimals: number;
    quoteDecimals: number;
    lpDecimals: number;
    baseReserve: BN__default;
    quoteReserve: BN__default;
    lpSupply: BN__default;
    startTime: BN__default;
}
/**
 * Full user keys that build transaction need
 */
interface LiquidityUserKeys {
    baseTokenAccount: PublicKey;
    quoteTokenAccount: PublicKey;
    lpTokenAccount: PublicKey;
    owner: PublicKey;
}
interface LiquidityAddInstructionParamsV4 {
    poolKeys: LiquidityPoolKeys;
    userKeys: LiquidityUserKeys;
    baseAmountIn: BigNumberish;
    quoteAmountIn: BigNumberish;
    fixedSide: AmountSide;
}
/**
 * Add liquidity instruction params
 */
type LiquidityAddInstructionParams = LiquidityAddInstructionParamsV4;
/**
 * Add liquidity transaction params
 */
interface LiquidityAddInstructionSimpleParams {
    connection: Connection;
    poolKeys: LiquidityPoolKeys;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    amountInA: CurrencyAmount | TokenAmount;
    amountInB: CurrencyAmount | TokenAmount;
    fixedSide: LiquiditySide;
    config?: {
        bypassAssociatedCheck?: boolean;
        checkCreateATAOwner?: boolean;
    };
}
interface LiquidityRemoveInstructionParamsV4 {
    poolKeys: LiquidityPoolKeys;
    userKeys: LiquidityUserKeys;
    amountIn: BigNumberish;
}
/**
 * Remove liquidity instruction params
 */
type LiquidityRemoveInstructionParams = LiquidityRemoveInstructionParamsV4;
/**
 * Remove liquidity transaction params
 */
interface LiquidityRemoveInstructionSimpleParams {
    connection: Connection;
    poolKeys: LiquidityPoolKeys;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    amountIn: TokenAmount;
    config?: {
        bypassAssociatedCheck?: boolean;
        checkCreateATAOwner?: boolean;
    };
}
interface LiquiditySwapFixedInInstructionParamsV4 {
    poolKeys: LiquidityPoolKeys;
    userKeys: {
        tokenAccountIn: PublicKey;
        tokenAccountOut: PublicKey;
        owner: PublicKey;
    };
    amountIn: BigNumberish;
    minAmountOut: BigNumberish;
}
interface LiquiditySwapFixedOutInstructionParamsV4 {
    poolKeys: LiquidityPoolKeys;
    userKeys: {
        tokenAccountIn: PublicKey;
        tokenAccountOut: PublicKey;
        owner: PublicKey;
    };
    maxAmountIn: BigNumberish;
    amountOut: BigNumberish;
}
/**
 * Swap instruction params
 */
interface LiquiditySwapInstructionParams {
    poolKeys: LiquidityPoolKeys;
    userKeys: {
        tokenAccountIn: PublicKey;
        tokenAccountOut: PublicKey;
        owner: PublicKey;
    };
    amountIn: BigNumberish;
    amountOut: BigNumberish;
    fixedSide: SwapSide;
}
/**
 * Swap transaction params
 */
interface LiquiditySwapInstructionSimpleParams {
    connection: Connection;
    poolKeys: LiquidityPoolKeys;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    amountIn: CurrencyAmount | TokenAmount;
    amountOut: CurrencyAmount | TokenAmount;
    fixedSide: SwapSide;
    config?: {
        bypassAssociatedCheck?: boolean;
        checkCreateATAOwner?: boolean;
    };
}
interface LiquidityInitPoolInstructionParamsV4 {
    poolKeys: LiquidityAssociatedPoolKeysV4;
    userKeys: {
        lpTokenAccount: PublicKey;
        payer: PublicKey;
    };
    startTime: BigNumberish;
}
/**
 * Init pool instruction params
 */
type LiquidityInitPoolInstructionParams = LiquidityInitPoolInstructionParamsV4;
/**
 * Init pool transaction params
 */
interface LiquidityInitPoolTransactionParams {
    connection: Connection;
    poolKeys: LiquidityAssociatedPoolKeysV4;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    baseAmount: CurrencyAmount | TokenAmount;
    quoteAmount: CurrencyAmount | TokenAmount;
    startTime?: BigNumberish;
    config?: {
        bypassAssociatedCheck?: boolean;
        checkCreateATAOwner?: boolean;
    };
}
/**
 * Fetch liquidity pool info params
 */
interface LiquidityFetchInfoParams {
    connection: Connection;
    poolKeys: LiquidityPoolKeys;
}
/**
 * Fetch liquidity multiple pool info params
 */
interface LiquidityFetchMultipleInfoParams {
    connection: Connection;
    pools: LiquidityPoolKeys[];
    config?: GetMultipleAccountsInfoConfig;
}
interface LiquidityComputeAnotherAmountParams {
    poolKeys: LiquidityPoolKeys;
    poolInfo: LiquidityPoolInfo;
    amount: CurrencyAmount | TokenAmount;
    anotherCurrency: Currency | Token;
    slippage: Percent;
}
declare const LIQUIDITY_FEES_NUMERATOR: BN__default;
declare const LIQUIDITY_FEES_DENOMINATOR: BN__default;
interface LiquidityComputeAmountOutParams {
    poolKeys: LiquidityPoolKeys;
    poolInfo: LiquidityPoolInfo;
    amountIn: CurrencyAmount | TokenAmount;
    currencyOut: Currency | Token;
    slippage: Percent;
}
interface LiquidityComputeAmountInParams extends Omit<LiquidityComputeAmountOutParams, 'amountIn' | 'currencyOut'> {
    amountOut: CurrencyAmount | TokenAmount;
    currencyIn: Currency | Token;
}
declare class Liquidity extends Base {
    static getStateLayout(version: number): LiquidityStateLayout;
    static getLayouts(version: number): {
        state: LiquidityStateLayout;
    };
    static getAssociatedId({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedAuthority({ programId }: {
        programId: PublicKey;
    }): {
        publicKey: PublicKey;
        nonce: number;
    };
    static getAssociatedBaseVault({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedQuoteVault({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedLpMint({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedLpVault({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedTargetOrders({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedWithdrawQueue({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedOpenOrders({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): PublicKey;
    static getAssociatedConfigId({ programId }: {
        programId: PublicKey;
    }): PublicKey;
    static getAssociatedPoolKeys({ version, marketVersion, marketId, baseMint, quoteMint, baseDecimals, quoteDecimals, programId, marketProgramId, }: {
        version: 4 | 5;
        marketVersion: 3;
        marketId: PublicKey;
        baseMint: PublicKey;
        quoteMint: PublicKey;
        baseDecimals: number;
        quoteDecimals: number;
        programId: PublicKey;
        marketProgramId: PublicKey;
    }): LiquidityAssociatedPoolKeys;
    static getCreatePoolFee({ connection, programId }: {
        connection: Connection;
        programId: PublicKey;
    }): Promise<BN__default>;
    static makeAddLiquidityInstruction(params: LiquidityAddInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: PublicKey[];
            instructionTypes: InstructionType[];
        };
    };
    static makeAddLiquidityInstructionSimple<T extends TxVersion>(params: LiquidityAddInstructionSimpleParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            lpTokenAccount: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeRemoveLiquidityInstruction(params: LiquidityRemoveInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: PublicKey[];
            instructionTypes: InstructionType[];
        };
    };
    static makeRemoveLiquidityInstructionSimple<T extends TxVersion>(params: LiquidityRemoveInstructionSimpleParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            lpTokenAccount: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeSwapInstruction(params: LiquiditySwapInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: PublicKey[];
            instructionTypes: InstructionType[];
        };
    };
    static makeSwapFixedInInstruction({ poolKeys, userKeys, amountIn, minAmountOut }: LiquiditySwapFixedInInstructionParamsV4, version: number): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: PublicKey[];
            instructionTypes: InstructionType[];
        };
    };
    static makeSwapFixedOutInstruction({ poolKeys, userKeys, maxAmountIn, amountOut }: LiquiditySwapFixedOutInstructionParamsV4, version: number): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: PublicKey[];
            instructionTypes: InstructionType[];
        };
    };
    static makeSwapInstructionSimple<T extends TxVersion>(params: LiquiditySwapInstructionSimpleParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeSimulatePoolInfoInstruction({ poolKeys }: {
        poolKeys: LiquidityPoolKeys;
    }): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: PublicKey[];
            instructionTypes: InstructionType[];
        };
    };
    static isV4(lsl: any): lsl is LiquidityStateV4;
    static makeCreatePoolV4InstructionV2Simple<T extends TxVersion>({ connection, programId, marketInfo, baseMintInfo, quoteMintInfo, baseAmount, quoteAmount, startTime, ownerInfo, associatedOnly, computeBudgetConfig, checkCreateATAOwner, makeTxVersion, lookupTableCache, feeDestinationId, }: {
        connection: Connection;
        programId: PublicKey;
        marketInfo: {
            marketId: PublicKey;
            programId: PublicKey;
        };
        baseMintInfo: {
            mint: PublicKey;
            decimals: number;
        };
        quoteMintInfo: {
            mint: PublicKey;
            decimals: number;
        };
        baseAmount: BN__default;
        quoteAmount: BN__default;
        startTime: BN__default;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        associatedOnly: boolean;
        checkCreateATAOwner: boolean;
        computeBudgetConfig?: ComputeBudgetConfig;
    } & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        feeDestinationId: PublicKey;
    }): Promise<{
        address: {
            programId: PublicKey;
            ammId: PublicKey;
            ammAuthority: PublicKey;
            ammOpenOrders: PublicKey;
            lpMint: PublicKey;
            coinMint: PublicKey;
            pcMint: PublicKey;
            coinVault: PublicKey;
            pcVault: PublicKey;
            withdrawQueue: PublicKey;
            ammTargetOrders: PublicKey;
            poolTempLp: PublicKey;
            marketProgramId: PublicKey;
            marketId: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCreatePoolV4InstructionV2({ programId, ammId, ammAuthority, ammOpenOrders, lpMint, coinMint, pcMint, coinVault, pcVault, ammTargetOrders, marketProgramId, marketId, userWallet, userCoinVault, userPcVault, userLpVault, nonce, openTime, coinAmount, pcAmount, lookupTableAddress, ammConfigId, feeDestinationId, }: {
        programId: PublicKey;
        ammId: PublicKey;
        ammAuthority: PublicKey;
        ammOpenOrders: PublicKey;
        lpMint: PublicKey;
        coinMint: PublicKey;
        pcMint: PublicKey;
        coinVault: PublicKey;
        pcVault: PublicKey;
        ammTargetOrders: PublicKey;
        marketProgramId: PublicKey;
        marketId: PublicKey;
        userWallet: PublicKey;
        userCoinVault: PublicKey;
        userPcVault: PublicKey;
        userLpVault: PublicKey;
        lookupTableAddress?: PublicKey;
        ammConfigId: PublicKey;
        feeDestinationId: PublicKey;
        nonce: number;
        openTime: BN__default;
        coinAmount: BN__default;
        pcAmount: BN__default;
    }): MakeInstructionOutType;
    static makeRemoveAllLpAndCreateClmmPosition<T extends TxVersion>({ connection, poolKeys, removeLpAmount, userKeys, clmmPoolKeys, createPositionInfo, farmInfo, computeBudgetConfig, checkCreateATAOwner, getEphemeralSigners, makeTxVersion, lookupTableCache, }: {
        connection: Connection;
        poolKeys: LiquidityPoolKeys;
        removeLpAmount: BN__default;
        clmmPoolKeys: ClmmPoolInfo;
        createPositionInfo: {
            tickLower: number;
            tickUpper: number;
            liquidity: BN__default;
            amountMaxA: BN__default;
            amountMaxB: BN__default;
        };
        userKeys: {
            tokenAccounts: TokenAccount[];
            owner: PublicKey;
            payer?: PublicKey;
        };
        farmInfo?: {
            poolKeys: FarmPoolKeys;
            amount: BN__default;
        };
        computeBudgetConfig?: ComputeBudgetConfig;
        checkCreateATAOwner: boolean;
        getEphemeralSigners?: (k: number) => any;
    } & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
    }): Promise<{
        address: {
            nftMint: PublicKey;
            tickArrayLower: PublicKey;
            tickArrayUpper: PublicKey;
            positionNftAccount: PublicKey;
            metadataAccount: PublicKey;
            personalPosition: PublicKey;
            protocolPosition: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    /**
     * Fetch all pools keys from on-chain data
     */
    static fetchAllPoolKeys(connection: Connection, programId: {
        4: PublicKey;
        5: PublicKey;
    }, config?: GetMultipleAccountsInfoConfig): Promise<LiquidityPoolKeys[]>;
    /**
     * Fetch liquidity pool's info
     */
    static fetchInfo({ connection, poolKeys }: LiquidityFetchInfoParams): Promise<LiquidityPoolInfo>;
    /**
     * Fetch multiple info of liquidity pools
     */
    static fetchMultipleInfo({ connection, pools, config, }: LiquidityFetchMultipleInfoParams): Promise<LiquidityPoolInfo[]>;
    static getEnabledFeatures(poolInfo: LiquidityPoolInfo): {
        swap: boolean;
        addLiquidity: boolean;
        removeLiquidity: boolean;
    };
    static includesToken(token: Token, poolKeys: LiquidityPoolKeys): boolean;
    /**
     * Get token side of liquidity pool
     * @param token - the token provided
     * @param poolKeys - the pool keys
     * @returns token side is `base` or `quote`
     */
    static _getTokenSide(token: Token, poolKeys: LiquidityPoolKeys): AmountSide;
    /**
     * Get tokens side of liquidity pool
     * @param tokenA - the token provided
     * @param tokenB - the token provided
     * @param poolKeys - the pool keys
     * @returns tokens side array
     */
    static _getTokensSide(tokenA: Token, tokenB: Token, poolKeys: LiquidityPoolKeys): AmountSide[];
    /**
     * Get currency amount side of liquidity pool
     * @param amount - the currency amount provided
     * @param poolKeys - the pool keys
     * @returns currency amount side is `base` or `quote`
     */
    static _getAmountSide(amount: CurrencyAmount | TokenAmount, poolKeys: LiquidityPoolKeys): AmountSide;
    /**
     * Get currencies amount side of liquidity pool
     * @param amountA - the currency amount provided
     * @param amountB - the currency amount provided
     * @param poolKeys - the pool keys
     * @returns currencies amount side array
     */
    static _getAmountsSide(amountA: CurrencyAmount | TokenAmount, amountB: CurrencyAmount | TokenAmount, poolKeys: LiquidityPoolKeys): AmountSide[];
    /**
     * Compute the another currency amount of add liquidity
     *
     * @param params - {@link LiquidityComputeAnotherAmountParams}
     *
     * @returns
     * anotherCurrencyAmount - currency amount without slippage
     * @returns
     * maxAnotherCurrencyAmount - currency amount with slippage
     *
     * @returns {@link CurrencyAmount}
     *
     * @example
     * ```
     * Liquidity.computeAnotherAmount({
     *   // 1%
     *   slippage: new Percent(1, 100)
     * })
     * ```
     */
    static computeAnotherAmount({ poolKeys, poolInfo, amount, anotherCurrency, slippage, }: LiquidityComputeAnotherAmountParams): {
        anotherAmount: CurrencyAmount;
        maxAnotherAmount: CurrencyAmount;
        liquidity: BN__default;
    } | {
        anotherAmount: TokenAmount;
        maxAnotherAmount: TokenAmount;
        liquidity: BN__default;
    };
    static _computePriceImpact(currentPrice: Price, amountIn: BN__default, amountOut: BN__default): Percent;
    static getRate(poolInfo: LiquidityPoolInfo): Price;
    /**
     * Compute output currency amount of swap
     *
     * @param params - {@link LiquidityComputeAmountOutParams}
     *
     * @returns
     * amountOut - currency amount without slippage
     * @returns
     * minAmountOut - currency amount with slippage
     */
    static computeAmountOut: ({ poolKeys, poolInfo, amountIn, currencyOut, slippage, }: LiquidityComputeAmountOutParams) => {
        amountOut: CurrencyAmount;
        minAmountOut: CurrencyAmount;
        currentPrice: Price;
        executionPrice: Price | null;
        priceImpact: Percent;
        fee: CurrencyAmount;
    } | {
        amountOut: TokenAmount;
        minAmountOut: TokenAmount;
        currentPrice: Price;
        executionPrice: Price | null;
        priceImpact: Percent;
        fee: CurrencyAmount;
    };
    /**
     * Compute input currency amount of swap
     *
     * @param params - {@link ComputeCurrencyAmountInParams}
     *
     * @returns
     * amountIn - currency amount without slippage
     * @returns
     * maxAmountIn - currency amount with slippage
     */
    static computeAmountIn({ poolKeys, poolInfo, amountOut, currencyIn, slippage }: LiquidityComputeAmountInParams): {
        amountIn: CurrencyAmount;
        maxAmountIn: CurrencyAmount;
        currentPrice: Price;
        executionPrice: Price | null;
        priceImpact: Percent;
    } | {
        amountIn: TokenAmount;
        maxAmountIn: TokenAmount;
        currentPrice: Price;
        executionPrice: Price | null;
        priceImpact: Percent;
    };
}

declare const MAINNET_OFFICIAL_LIQUIDITY_POOLS: string[];
declare const TESTNET_OFFICIAL_LIQUIDITY_POOLS: never[];
declare const DEVNET_OFFICIAL_LIQUIDITY_POOLS: never[];

declare const ModelDataPubkey: PublicKey;
declare const DataElement: Structure<BN, "", {
    price: BN;
    x: BN;
    y: BN;
}>;
declare const ModelDataInfo: Structure<BN | {
    price: BN;
    x: BN;
    y: BN;
}[], "", {
    status: BN;
    accountType: BN;
    multiplier: BN;
    validDataCount: BN;
    DataElement: {
        price: BN;
        x: BN;
        y: BN;
    }[];
}>;
interface StableModelLayout {
    accountType: number;
    status: number;
    multiplier: number;
    validDataCount: number;
    DataElement: {
        x: number;
        y: number;
        price: number;
    }[];
}
declare function getDyByDxBaseIn(layoutData: StableModelLayout, xReal: number, yReal: number, dxReal: number): number;
declare function getDxByDyBaseIn(layoutData: StableModelLayout, xReal: number, yReal: number, dyReal: number): number;
declare function formatLayout(buffer: Buffer): StableModelLayout;
declare function getStablePrice(layoutData: StableModelLayout, coinReal: number, pcReal: number, baseCoin: boolean): number;

type LiquidityVersion = 4 | 5;
interface LiquidityPoolBaseInfo {
    readonly id: string;
    readonly lp: LpTokenInfo;
}
interface LiquidityPoolJsonInfo {
    readonly id: string;
    readonly baseMint: string;
    readonly quoteMint: string;
    readonly lpMint: string;
    readonly baseDecimals: number;
    readonly quoteDecimals: number;
    readonly lpDecimals: number;
    readonly version: number;
    readonly programId: string;
    readonly authority: string;
    readonly baseVault: string;
    readonly quoteVault: string;
    readonly lpVault: string;
    readonly openOrders: string;
    readonly targetOrders: string;
    readonly withdrawQueue: string;
    readonly marketVersion: number;
    readonly marketProgramId: string;
    readonly marketId: string;
    readonly marketAuthority: string;
    readonly marketBaseVault: string;
    readonly marketQuoteVault: string;
    readonly marketBids: string;
    readonly marketAsks: string;
    readonly marketEventQueue: string;
}
interface LiquidityPoolsJsonFile extends JsonFileMetaData {
    readonly official: LiquidityPoolJsonInfo[];
    readonly unOfficial: LiquidityPoolJsonInfo[];
}

interface NativeTokenInfo {
    readonly symbol: string;
    readonly name: string;
    readonly decimals: number;
}
type ExtensionKey = 'coingeckoId' | 'website' | 'whitepaper';
type Extensions = {
    [key in ExtensionKey]?: string;
};
interface SplTokenInfo extends NativeTokenInfo {
    readonly mint: string;
    readonly extensions: Extensions;
}
type SplTokens = {
    [T in string]: SplTokenInfo;
};
interface LpTokenInfo extends NativeTokenInfo {
    readonly mint: string;
    readonly base: SplTokenInfo;
    readonly quote: SplTokenInfo;
    readonly version: LiquidityVersion;
}
type LpTokens = {
    [T in string]: LpTokenInfo;
};
interface SplTokenJsonInfo {
    readonly symbol: string;
    readonly name: string;
    readonly mint: string;
    readonly decimals: number;
    readonly extensions: Extensions;
}
interface LpTokenJsonInfo {
    readonly symbol: string;
    readonly name: string;
    readonly mint: string;
    readonly base: string;
    readonly quote: string;
    readonly decimals: number;
    readonly version: LiquidityVersion;
}
type SplTokensJsonInfo = {
    [T in string]: SplTokenJsonInfo;
};
type LpTokensJsonInfo = {
    [T in string]: LpTokenJsonInfo;
};
interface TokensJsonFile extends JsonFileMetaData {
    readonly spl: SplTokensJsonInfo;
    readonly lp: LpTokensJsonInfo;
}

declare const DEVNET_SPL_TOKENS: SplTokens;
declare const DEVNET_LP_TOKENS: LpTokens;

declare const MAINNET_SPL_TOKENS: SplTokens;
declare const MAINNET_LP_TOKENS: LpTokens;

declare const TESTNET_SPL_TOKENS: SplTokens;
declare const TESTNET_LP_TOKENS: LpTokens;

declare const SOL: NativeTokenInfo;
declare const WSOL: SplTokenInfo;

/**
 * Token list
 */
declare class TokenList {
    private tokenList;
    constructor(tokenList: (SplTokenInfo | LpTokenInfo)[]);
    /**
     * Filter token by mint of token list.
     *
     * @param mint - Token's mint address
     */
    filterByMint: (mint: string) => (SplTokenInfo | LpTokenInfo)[];
    /**
     * Filter unique token by mint of token list, must and can only have one result.
     */
    filterUniqueByMint: <T extends "all" | "spl" | "lp">(mint: string, tokenType?: T | "all" | "spl" | "lp") => T extends "all" ? SplTokenInfo | LpTokenInfo : T extends "spl" ? SplTokenInfo : LpTokenInfo;
    /**
     * Get list of token list
     */
    getList: () => (SplTokenInfo | LpTokenInfo)[];
}

type FarmVersion = 3 | 4 | 5 | 6;
interface FarmPoolBaseInfo {
    readonly id: string;
    readonly lp: LpTokenInfo | SplTokenInfo;
    readonly version: number;
}
interface FarmPoolJsonInfoV1 {
    readonly id: string;
    readonly lpMint: string;
    readonly rewardMints: string[];
    readonly version: number;
    readonly programId: string;
    readonly authority: string;
    readonly lpVault: string;
    readonly rewardVaults: string[];
}
interface RewardInfoV3V4V5 {
    readonly rewardMint: string;
    readonly rewardVault: string;
}
interface RewardInfoV6 {
    readonly rewardMint: string;
    readonly rewardVault: string;
    readonly rewardOpenTime: number;
    readonly rewardEndTime: number;
    readonly rewardPerSecond: number;
    readonly rewardSender: string;
    readonly rewardType: string;
}
interface FarmPoolJsonInfoV3V4V5 {
    readonly id: string;
    readonly lpMint: string;
    readonly version: 3 | 4 | 5;
    readonly programId: string;
    readonly authority: string;
    readonly lpVault: string;
    readonly upcoming: boolean;
    readonly rewardInfos: RewardInfoV3V4V5[];
}
interface FarmPoolJsonInfoV6 {
    readonly id: string;
    readonly lpMint: string;
    readonly version: 6;
    readonly programId: string;
    readonly authority: string;
    readonly lpVault: string;
    readonly rewardPeriodMax: number;
    readonly rewardPeriodMin: number;
    readonly rewardPeriodExtend: number;
    readonly creator: string;
    readonly upcoming: boolean;
    readonly rewardInfos: RewardInfoV6[];
}
type FarmPoolJsonInfo = FarmPoolJsonInfoV3V4V5 | FarmPoolJsonInfoV6;
interface FarmPoolsJsonFile extends JsonFileMetaData {
    readonly official: FarmPoolJsonInfoV1[];
}

declare const REAL_FARM_STATE_LAYOUT_V3: Structure<PublicKey | BN__default, "", {
    nonce: BN__default;
    state: BN__default;
    lpVault: PublicKey;
    rewardVault: PublicKey;
    totalReward: BN__default;
    perShareReward: BN__default;
    lastSlot: BN__default;
    perSlotReward: BN__default;
}>;
declare const REAL_FARM_STATE_LAYOUT_V5: Structure<number | PublicKey | Buffer | BN__default, "", {
    nonce: BN__default;
    state: BN__default;
    lpVault: PublicKey;
    option: number;
    lastSlot: BN__default;
    rewardVaultA: PublicKey;
    totalRewardA: BN__default;
    perShareRewardA: BN__default;
    perSlotRewardA: BN__default;
    rewardVaultB: PublicKey;
    totalRewardB: BN__default;
    perShareRewardB: BN__default;
    perSlotRewardB: BN__default;
}>;
declare const REAL_FARM_STATE_LAYOUT_V6: Structure<PublicKey | BN__default | BN__default[] | {
    padding: BN__default[];
    rewardState: BN__default;
    rewardClaimed: BN__default;
    rewardVault: PublicKey;
    rewardMint: PublicKey;
    totalReward: BN__default;
    rewardOpenTime: BN__default;
    rewardEndTime: BN__default;
    rewardLastUpdateTime: BN__default;
    totalRewardEmissioned: BN__default;
    rewardPerSecond: BN__default;
    accRewardPerShare: BN__default;
    rewardSender: PublicKey;
    rewardType: BN__default;
}[], "", {
    nonce: BN__default;
    state: BN__default;
    lpMint: PublicKey;
    lpVault: PublicKey;
    padding: BN__default[];
    creator: PublicKey;
    rewardInfos: {
        padding: BN__default[];
        rewardState: BN__default;
        rewardClaimed: BN__default;
        rewardVault: PublicKey;
        rewardMint: PublicKey;
        totalReward: BN__default;
        rewardOpenTime: BN__default;
        rewardEndTime: BN__default;
        rewardLastUpdateTime: BN__default;
        totalRewardEmissioned: BN__default;
        rewardPerSecond: BN__default;
        accRewardPerShare: BN__default;
        rewardSender: PublicKey;
        rewardType: BN__default;
    }[];
    validRewardTokenNum: BN__default;
    rewardMultiplier: BN__default;
    rewardPeriodMax: BN__default;
    rewardPeriodMin: BN__default;
    rewardPeriodExtend: BN__default;
}>;
declare const FARM_STATE_LAYOUT_V3: GetStructureFromLayoutSchema<{
    version: 3;
    rewardInfos: {
        rewardVault: PublicKey;
        totalReward: BN__default;
        perSlotReward: BN__default;
        perShareReward: BN__default;
    }[];
} & {
    nonce: BN__default;
    state: BN__default;
    lpVault: PublicKey;
    rewardVault: PublicKey;
    totalReward: BN__default;
    perShareReward: BN__default;
    lastSlot: BN__default;
    perSlotReward: BN__default;
}>;
declare const FARM_STATE_LAYOUT_V5: GetStructureFromLayoutSchema<{
    version: 5;
    rewardInfos: {
        rewardVault: PublicKey;
        totalReward: BN__default;
        perSlotReward: BN__default;
        perShareReward: BN__default;
    }[];
} & {
    nonce: BN__default;
    state: BN__default;
    lpVault: PublicKey;
    option: number;
    lastSlot: BN__default;
    rewardVaultA: PublicKey;
    totalRewardA: BN__default;
    perShareRewardA: BN__default;
    perSlotRewardA: BN__default;
    rewardVaultB: PublicKey;
    totalRewardB: BN__default;
    perShareRewardB: BN__default;
    perSlotRewardB: BN__default;
}>;
declare const FARM_STATE_LAYOUT_V6: GetStructureFromLayoutSchema<{
    version: 6;
    rewardInfos: {
        rewardState: BN__default;
        rewardOpenTime: BN__default;
        rewardEndTime: BN__default;
        rewardLastUpdateTime: BN__default;
        totalReward: BN__default;
        totalRewardEmissioned: BN__default;
        rewardClaimed: BN__default;
        rewardPerSecond: BN__default;
        accRewardPerShare: BN__default;
        rewardVault: PublicKey;
        rewardMint: PublicKey;
        rewardSender: PublicKey;
        rewardType: keyof typeof poolTypeV6;
    }[];
} & {
    nonce: BN__default;
    state: BN__default;
    lpMint: PublicKey;
    lpVault: PublicKey;
    padding: BN__default[];
    creator: PublicKey;
    rewardInfos: {
        padding: BN__default[];
        rewardState: BN__default;
        rewardClaimed: BN__default;
        rewardVault: PublicKey;
        rewardMint: PublicKey;
        totalReward: BN__default;
        rewardOpenTime: BN__default;
        rewardEndTime: BN__default;
        rewardLastUpdateTime: BN__default;
        totalRewardEmissioned: BN__default;
        rewardPerSecond: BN__default;
        accRewardPerShare: BN__default;
        rewardSender: PublicKey;
        rewardType: BN__default;
    }[];
    validRewardTokenNum: BN__default;
    rewardMultiplier: BN__default;
    rewardPeriodMax: BN__default;
    rewardPeriodMin: BN__default;
    rewardPeriodExtend: BN__default;
}>;
type FarmStateLayoutV3 = typeof FARM_STATE_LAYOUT_V3;
type FarmStateLayoutV5 = typeof FARM_STATE_LAYOUT_V5;
type FarmStateLayoutV6 = typeof FARM_STATE_LAYOUT_V6;
type FarmStateLayout = FarmStateLayoutV3 | FarmStateLayoutV5 | FarmStateLayoutV6;
type FarmStateV3 = GetStructureSchema<FarmStateLayoutV3>;
type FarmStateV5 = GetStructureSchema<FarmStateLayoutV5>;
type FarmStateV6 = GetStructureSchema<FarmStateLayoutV6>;
type FarmState = FarmStateV3 | FarmStateV5 | FarmStateV6;
declare const FARM_LEDGER_LAYOUT_V3_1: Structure<PublicKey | BN__default | BN__default[], "", {
    owner: PublicKey;
    state: BN__default;
    id: PublicKey;
    deposited: BN__default;
    rewardDebts: BN__default[];
}>;
declare const FARM_LEDGER_LAYOUT_V3_2: Structure<PublicKey | BN__default | BN__default[], "", {
    owner: PublicKey;
    state: BN__default;
    id: PublicKey;
    deposited: BN__default;
    rewardDebts: BN__default[];
    voteLockedBalance: BN__default;
}>;
declare const FARM_LEDGER_LAYOUT_V5_1: Structure<PublicKey | BN__default | BN__default[], "", {
    owner: PublicKey;
    state: BN__default;
    id: PublicKey;
    deposited: BN__default;
    rewardDebts: BN__default[];
}>;
declare const FARM_LEDGER_LAYOUT_V5_2: Structure<PublicKey | BN__default | BN__default[], "", {
    owner: PublicKey;
    state: BN__default;
    id: PublicKey;
    deposited: BN__default;
    rewardDebts: BN__default[];
}>;
declare const FARM_LEDGER_LAYOUT_V6_1: Structure<PublicKey | BN__default | BN__default[], "", {
    owner: PublicKey;
    state: BN__default;
    id: PublicKey;
    deposited: BN__default;
    rewardDebts: BN__default[];
}>;
type FarmLedgerLayoutV3_1 = typeof FARM_LEDGER_LAYOUT_V3_1;
type FarmLedgerLayoutV3_2 = typeof FARM_LEDGER_LAYOUT_V3_2;
type FarmLedgerLayoutV5_1 = typeof FARM_LEDGER_LAYOUT_V5_1;
type FarmLedgerLayoutV5_2 = typeof FARM_LEDGER_LAYOUT_V5_2;
type FarmLedgerLayoutV6_1 = typeof FARM_LEDGER_LAYOUT_V6_1;
type FarmLedgerLayout = FarmLedgerLayoutV3_1 | FarmLedgerLayoutV3_2 | FarmLedgerLayoutV5_1 | FarmLedgerLayoutV5_2 | FarmLedgerLayoutV6_1;
type FarmLedgerV3_1 = GetStructureSchema<FarmLedgerLayoutV3_1>;
type FarmLedgerV3_2 = GetStructureSchema<FarmLedgerLayoutV3_2>;
type FarmLedgerV5_1 = GetStructureSchema<FarmLedgerLayoutV5_1>;
type FarmLedgerV5_2 = GetStructureSchema<FarmLedgerLayoutV5_2>;
type FarmLedgerV6_1 = GetStructureSchema<FarmLedgerLayoutV6_1>;
type FarmLedger = FarmLedgerV3_1 | FarmLedgerV3_2 | FarmLedgerV5_1 | FarmLedgerV5_2 | FarmLedgerV6_1;
declare const FARM_VERSION_TO_STATE_LAYOUT: {
    [version in FarmVersion]?: FarmStateLayout;
} & {
    [version: number]: FarmStateLayout;
};
declare const FARM_VERSION_TO_LEDGER_LAYOUT: {
    [version in FarmVersion]?: FarmLedgerLayout;
} & {
    [version: number]: FarmLedgerLayout;
};
declare const VoterVotingMintConfig: Structure<number[] | PublicKey | BN__default | BN__default[], "", {
    mint: PublicKey;
    grantAuthority: PublicKey;
    baselineVoteWeightScaledFactor: BN__default;
    maxExtraLockupVoteWeightScaledFactor: BN__default;
    lockupSaturationSecs: BN__default;
    digitShift: BN__default;
    reserved1: number[];
    reserved2: BN__default[];
}>;
declare const VoterRegistrar: Structure<number | number[] | PublicKey | Buffer | BN__default | BN__default[] | {
    mint: PublicKey;
    grantAuthority: PublicKey;
    baselineVoteWeightScaledFactor: BN__default;
    maxExtraLockupVoteWeightScaledFactor: BN__default;
    lockupSaturationSecs: BN__default;
    digitShift: BN__default;
    reserved1: number[];
    reserved2: BN__default[];
}[], "", {
    bump: number;
    reserved1: number[];
    reserved2: number[];
    governanceProgramId: PublicKey;
    realm: PublicKey;
    realmGoverningTokenMint: PublicKey;
    realmAuthority: PublicKey;
    votingMints: {
        mint: PublicKey;
        grantAuthority: PublicKey;
        baselineVoteWeightScaledFactor: BN__default;
        maxExtraLockupVoteWeightScaledFactor: BN__default;
        lockupSaturationSecs: BN__default;
        digitShift: BN__default;
        reserved1: number[];
        reserved2: BN__default[];
    }[];
    timeOffset: BN__default;
    reserved3: BN__default[];
}>;
declare const VoterLockup: Structure<number | number[] | BN__default, "", {
    startTime: BN__default;
    endTime: BN__default;
    kind: number;
    reserved: number[];
}>;
declare const VoterDepositEntry: Structure<number | boolean | number[] | BN__default | {
    startTime: BN__default;
    endTime: BN__default;
    kind: number;
    reserved: number[];
}[], "", {
    allowClawback: boolean;
    reserved: number[];
    lockup: {
        startTime: BN__default;
        endTime: BN__default;
        kind: number;
        reserved: number[];
    }[];
    amountDeposited_native: BN__default;
    amountInitiallyLockedNative: BN__default;
    isUsed: boolean;
    votingMintConfigIdx: number;
}>;
declare const Voter: Structure<number | number[] | PublicKey | Buffer | {
    allowClawback: boolean;
    reserved: number[];
    lockup: {
        startTime: BN__default;
        endTime: BN__default;
        kind: number;
        reserved: number[];
    }[];
    amountDeposited_native: BN__default;
    amountInitiallyLockedNative: BN__default;
    isUsed: boolean;
    votingMintConfigIdx: number;
}[], "", {
    voterBump: number;
    reserved: number[];
    voterAuthority: PublicKey;
    registrar: PublicKey;
    deposits: {
        allowClawback: boolean;
        reserved: number[];
        lockup: {
            startTime: BN__default;
            endTime: BN__default;
            kind: number;
            reserved: number[];
        }[];
        amountDeposited_native: BN__default;
        amountInitiallyLockedNative: BN__default;
        isUsed: boolean;
        votingMintConfigIdx: number;
    }[];
    voterWweightRecordBump: number;
}>;

declare const poolTypeV6: {
    readonly 'Standard SPL': 0;
    readonly 'Option tokens': 1;
};
type FarmPoolKeys = {
    readonly id: PublicKey;
    readonly lpMint: PublicKey;
    readonly version: number;
    readonly programId: PublicKey;
    readonly authority: PublicKey;
    readonly lpVault: PublicKey;
    readonly upcoming: boolean;
    readonly rewardInfos: ({
        readonly rewardMint: PublicKey;
        readonly rewardVault: PublicKey;
    } | {
        readonly rewardMint: PublicKey;
        readonly rewardVault: PublicKey;
        readonly rewardOpenTime: number;
        readonly rewardEndTime: number;
        readonly rewardPerSecond: number;
        readonly rewardType: keyof typeof poolTypeV6;
    })[];
};
/**
 * Full user keys that build transaction need
 */
interface FarmUserKeys {
    ledger: PublicKey;
    auxiliaryLedgers?: PublicKey[];
    lpTokenAccount: PublicKey;
    rewardTokenAccounts: PublicKey[];
    owner: PublicKey;
}
interface FarmRewardInfo {
    rewardMint: PublicKey;
    rewardPerSecond: BigNumberish;
    rewardOpenTime: BigNumberish;
    rewardEndTime: BigNumberish;
    rewardType: keyof typeof poolTypeV6;
}
interface FarmDepositInstructionParams {
    poolKeys: FarmPoolKeys;
    userKeys: FarmUserKeys;
    amount: BigNumberish;
}
type FarmWithdrawInstructionParams = FarmDepositInstructionParams;
interface FarmCreateAssociatedLedgerAccountInstructionParams {
    poolKeys: FarmPoolKeys;
    userKeys: {
        ledger: PublicKey;
        owner: PublicKey;
    };
}
interface FarmCreateInstructionParamsV6 {
    version: 6;
    programId: PublicKey;
    lpMint: PublicKey;
    rewardInfos: {
        rewardMint: PublicKey;
        rewardPerSecond: BigNumberish;
        rewardOpenTime: BigNumberish;
        rewardEndTime: BigNumberish;
        rewardType: keyof typeof poolTypeV6;
    }[];
    lockInfo: {
        lockMint: PublicKey;
        lockVault: PublicKey;
    };
}
type FarmCreateInstructionParams = FarmCreateInstructionParamsV6;
interface FarmRestartInstructionParamsV6 {
    connection: Connection;
    poolKeys: FarmPoolKeys;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    newRewardInfo: FarmRewardInfo;
}
type FarmRestartInstructionParams = FarmRestartInstructionParamsV6;
interface FarmCreatorWithdrawRewardInstructionParamsV6 {
    poolKeys: FarmPoolKeys;
    userKeys: {
        userRewardToken: PublicKey;
        owner: PublicKey;
        payer?: PublicKey;
    };
    withdrawMint: PublicKey;
}
type FarmCreatorWithdrawRewardInstructionParams = FarmCreatorWithdrawRewardInstructionParamsV6;
interface FarmCreatorWithdrawRewardInstructionSimpleParamsV6 {
    connection: Connection;
    poolKeys: FarmPoolKeys;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    withdrawMint: PublicKey;
}
type FarmCreatorWithdrawRewardInstructionSimpleParams = FarmCreatorWithdrawRewardInstructionSimpleParamsV6;
interface FarmCreatorAddRewardTokenInstructionParamsV6 {
    connection: Connection;
    poolKeys: FarmPoolKeys;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    newRewardInfo: FarmRewardInfo;
}
type FarmCreatorAddRewardTokenInstructionParams = FarmCreatorAddRewardTokenInstructionParamsV6;
interface MakeCreateFarmInstructionParamsV6 {
    connection: Connection;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    poolInfo: FarmCreateInstructionParams;
}
type makeCreateFarmInstructionParams = MakeCreateFarmInstructionParamsV6;
interface MakeCreateFarmInstructionParamsV6Simple {
    connection: Connection;
    userKeys: {
        tokenAccounts: TokenAccount[];
        owner: PublicKey;
        payer?: PublicKey;
    };
    poolInfo: FarmCreateInstructionParams;
}
type makeCreateFarmInstructionSimpleParams = MakeCreateFarmInstructionParamsV6Simple;
interface FarmFetchMultipleInfoParams {
    connection: Connection;
    pools: FarmPoolKeys[];
    owner?: PublicKey;
    config?: GetMultipleAccountsInfoConfig;
    chainTime: number;
}
interface FarmFetchMultipleInfoReturnItem {
    apiPoolInfo: FarmPoolKeys;
    state: FarmState;
    lpVault: SplAccount;
    ledger?: FarmLedger;
    wrapped?: {
        pendingRewards: BN__default[];
    };
}
interface FarmFetchMultipleInfoReturn {
    [id: string]: FarmFetchMultipleInfoReturnItem;
}
declare class Farm extends Base {
    static getStateLayout(version: number): FarmStateLayout;
    static getLedgerLayout(version: number): FarmLedgerLayout;
    static getLayouts(version: number): {
        state: FarmStateLayout;
        ledger: FarmLedgerLayout;
    };
    static getAssociatedAuthority({ programId, poolId }: {
        programId: PublicKey;
        poolId: PublicKey;
    }): {
        publicKey: PublicKey;
        nonce: number;
    };
    static getAssociatedLedgerAccount({ programId, poolId, owner, version, }: {
        programId: PublicKey;
        poolId: PublicKey;
        owner: PublicKey;
        version: 6 | 5 | 3;
    }): PublicKey;
    static getAssociatedLedgerPoolAccount({ programId, poolId, mint, type, }: {
        programId: PublicKey;
        poolId: PublicKey;
        mint: PublicKey;
        type: 'lpVault' | 'rewardVault';
    }): PublicKey;
    static makeDepositInstruction(params: FarmDepositInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeDepositInstructionV3({ poolKeys, userKeys, amount }: FarmDepositInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeDepositInstructionV5({ poolKeys, userKeys, amount }: FarmDepositInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeDepositInstructionV6({ poolKeys, userKeys, amount }: FarmDepositInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeWithdrawInstruction(params: FarmWithdrawInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeWithdrawInstructionV3({ poolKeys, userKeys, amount }: FarmWithdrawInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeWithdrawInstructionV5({ poolKeys, userKeys, amount }: FarmWithdrawInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeWithdrawInstructionV6({ poolKeys, userKeys, amount }: FarmWithdrawInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeCreateAssociatedLedgerAccountInstruction(params: FarmCreateAssociatedLedgerAccountInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeCreateAssociatedLedgerAccountInstructionV3({ poolKeys, userKeys, }: FarmCreateAssociatedLedgerAccountInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeCreateAssociatedLedgerAccountInstructionV5({ poolKeys, userKeys, }: FarmCreateAssociatedLedgerAccountInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeCreateFarmInstruction({ connection, userKeys, poolInfo }: makeCreateFarmInstructionParams): Promise<{
        address: {
            farmId: PublicKey;
        };
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: Signer[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    }>;
    static makeCreateFarmInstructionV6({ connection, userKeys, poolInfo }: MakeCreateFarmInstructionParamsV6): Promise<{
        address: {
            farmId: PublicKey;
        };
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: Signer[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    }>;
    static makeCreatorWithdrawFarmRewardInstruction(params: FarmCreatorWithdrawRewardInstructionParams): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static makeCreatorWithdrawFarmRewardInstructionV6({ poolKeys, userKeys, withdrawMint, }: FarmCreatorWithdrawRewardInstructionParamsV6): {
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: InstructionType[];
        };
    };
    static fetchMultipleInfoAndUpdate({ connection, pools, owner, config, chainTime, }: FarmFetchMultipleInfoParams): Promise<FarmFetchMultipleInfoReturn>;
    static updatePoolInfo(poolInfo: FarmState, lpVault: SplAccount, slot: number, chainTime: number): FarmState;
    static makeCreatorWithdrawFarmRewardInstructionSimple<T extends TxVersion>(params: FarmCreatorWithdrawRewardInstructionSimpleParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCreatorWithdrawFarmRewardInstructionV6Simple<T extends TxVersion>({ connection, poolKeys, userKeys, withdrawMint, makeTxVersion, lookupTableCache, computeBudgetConfig, }: FarmCreatorWithdrawRewardInstructionSimpleParamsV6 & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCreateFarmInstructionSimple<T extends TxVersion>(params: makeCreateFarmInstructionSimpleParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            farmId: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCreateFarmInstructionV6Simple<T extends TxVersion>({ connection, userKeys, poolInfo, makeTxVersion, lookupTableCache, computeBudgetConfig, }: MakeCreateFarmInstructionParamsV6Simple & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {
            farmId: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeRestartFarmInstructionSimple<T extends TxVersion>(params: FarmRestartInstructionParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeRestartFarmInstructionV6Simple<T extends TxVersion>({ connection, poolKeys, userKeys, newRewardInfo, makeTxVersion, lookupTableCache, computeBudgetConfig, }: FarmRestartInstructionParamsV6 & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeFarmCreatorAddRewardTokenInstructionSimple<T extends TxVersion>(params: FarmCreatorAddRewardTokenInstructionParams & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeFarmCreatorAddRewardTokenInstructionV6Simple<T extends TxVersion>({ connection, poolKeys, userKeys, newRewardInfo, makeTxVersion, lookupTableCache, computeBudgetConfig, }: FarmCreatorAddRewardTokenInstructionParamsV6 & {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeDepositInstructionSimple<T extends TxVersion>({ connection, poolKeys, fetchPoolInfo, ownerInfo, amount, associatedOnly, checkCreateATAOwner, makeTxVersion, lookupTableCache, computeBudgetConfig, }: {
        connection: Connection;
        poolKeys: FarmPoolKeys;
        fetchPoolInfo: FarmFetchMultipleInfoReturnItem;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        amount: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeWithdrawInstructionSimple<T extends TxVersion>({ connection, fetchPoolInfo, ownerInfo, amount, associatedOnly, checkCreateATAOwner, makeTxVersion, lookupTableCache, computeBudgetConfig, }: {
        connection: Connection;
        fetchPoolInfo: FarmFetchMultipleInfoReturnItem;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        amount: BN__default;
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeHarvestAllRewardInstructionSimple<T extends TxVersion>({ connection, fetchPoolInfos, ownerInfo, associatedOnly, checkCreateATAOwner, makeTxVersion, lookupTableCache, computeBudgetConfig, }: {
        connection: Connection;
        fetchPoolInfos: FarmFetchMultipleInfoReturn;
        ownerInfo: {
            feePayer: PublicKey;
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            useSOLBalance?: boolean;
        };
        associatedOnly?: boolean;
        checkCreateATAOwner?: boolean;
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    /**
     * @deprecated the method is **DANGEROUS**, please don't use
     */
    static makeV1InfoToV2PdaAndHarvestSimple<T extends TxVersion>({ connection, wallet, tokenAccounts, programIdV3, programIdV5, makeTxVersion, lookupTableCache, computeBudgetConfig, }: {
        connection: Connection;
        wallet: PublicKey;
        tokenAccounts: TokenAccount[];
        programIdV3: PublicKey;
        programIdV5: PublicKey;
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeDepositTokenInstruction({ connection, programId, governanceProgramId, voteWeightAddinProgramId, realm, communityTokenMint, owner, poolId, }: {
        connection: Connection;
        programId: PublicKey;
        governanceProgramId: PublicKey;
        voteWeightAddinProgramId: PublicKey;
        realm: PublicKey;
        communityTokenMint: PublicKey;
        owner: PublicKey;
        poolId: PublicKey;
    }): Promise<{
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: any[];
        };
    }>;
    static makeWithdrawTokenInstruction({ connection, programId, governanceProgramId, voteWeightAddinProgramId, realm, communityTokenMint, owner, poolId, }: {
        connection: Connection;
        programId: PublicKey;
        governanceProgramId: PublicKey;
        voteWeightAddinProgramId: PublicKey;
        realm: PublicKey;
        communityTokenMint: PublicKey;
        owner: PublicKey;
        poolId: PublicKey;
    }): Promise<{
        address: {};
        innerTransaction: {
            instructions: TransactionInstruction[];
            signers: never[];
            lookupTableAddress: never[];
            instructionTypes: any[];
        };
    }>;
}

declare const MAINNET_FARM_POOLS: FarmPoolBaseInfo[];
declare const TESTNET_FARM_POOLS: FarmPoolBaseInfo[];
declare const DEVNET_FARM_POOLS: FarmPoolBaseInfo[];

type SerumVersion = 3;

declare const _SERUM_PROGRAM_ID_V3 = "9xQeWvG816bUx9EPjHmaT23yvVM2ZWbrrpZb9PusVFin";
declare const SERUM_PROGRAM_ID_V3: PublicKey;
declare const SERUM_PROGRAMID_TO_VERSION: {
    [key: string]: SerumVersion;
};
declare const SERUM_VERSION_TO_PROGRAMID: {
    [key in SerumVersion]?: PublicKey;
} & {
    [K: number]: PublicKey;
};

declare const MARKET_STATE_LAYOUT_V3: Structure<_solana_web3_js.PublicKey | Buffer | BN, "", {
    baseLotSize: BN;
    quoteLotSize: BN;
    baseVault: _solana_web3_js.PublicKey;
    quoteVault: _solana_web3_js.PublicKey;
    baseMint: _solana_web3_js.PublicKey;
    quoteMint: _solana_web3_js.PublicKey;
    ownAddress: _solana_web3_js.PublicKey;
    vaultSignerNonce: BN;
    baseDepositsTotal: BN;
    baseFeesAccrued: BN;
    quoteDepositsTotal: BN;
    quoteFeesAccrued: BN;
    quoteDustThreshold: BN;
    requestQueue: _solana_web3_js.PublicKey;
    eventQueue: _solana_web3_js.PublicKey;
    bids: _solana_web3_js.PublicKey;
    asks: _solana_web3_js.PublicKey;
    feeRateBps: BN;
    referrerRebatesAccrued: BN;
}>;
type MarketStateLayoutV3 = typeof MARKET_STATE_LAYOUT_V3;
type MarketStateLayout = MarketStateLayoutV3;
type MarketStateV3 = GetStructureSchema<MarketStateLayoutV3>;
type MarketState = MarketStateV3;
declare const MARKET_VERSION_TO_STATE_LAYOUT: {
    [version: number]: MarketStateLayout;
};

declare class Market {
    static getProgramId(version: number): PublicKey;
    static getVersion(programId: PublicKey): 3;
    static getStateLayout(version: number): Structure<PublicKey | Buffer | BN, "", {
        baseLotSize: BN;
        quoteLotSize: BN;
        baseVault: PublicKey;
        quoteVault: PublicKey;
        baseMint: PublicKey;
        quoteMint: PublicKey;
        ownAddress: PublicKey;
        vaultSignerNonce: BN;
        baseDepositsTotal: BN;
        baseFeesAccrued: BN;
        quoteDepositsTotal: BN;
        quoteFeesAccrued: BN;
        quoteDustThreshold: BN;
        requestQueue: PublicKey;
        eventQueue: PublicKey;
        bids: PublicKey;
        asks: PublicKey;
        feeRateBps: BN;
        referrerRebatesAccrued: BN;
    }>;
    static getLayouts(version: number): {
        state: Structure<PublicKey | Buffer | BN, "", {
            baseLotSize: BN;
            quoteLotSize: BN;
            baseVault: PublicKey;
            quoteVault: PublicKey;
            baseMint: PublicKey;
            quoteMint: PublicKey;
            ownAddress: PublicKey;
            vaultSignerNonce: BN;
            baseDepositsTotal: BN;
            baseFeesAccrued: BN;
            quoteDepositsTotal: BN;
            quoteFeesAccrued: BN;
            quoteDustThreshold: BN;
            requestQueue: PublicKey;
            eventQueue: PublicKey;
            bids: PublicKey;
            asks: PublicKey;
            feeRateBps: BN;
            referrerRebatesAccrued: BN;
        }>;
    };
    static getAssociatedAuthority({ programId, marketId }: {
        programId: PublicKey;
        marketId: PublicKey;
    }): {
        publicKey: PublicKey;
        nonce: number;
    };
}

type PoolType = ClmmPoolInfo | ApiPoolInfoItem;
type RoutePathType = {
    [routeMint: string]: {
        mintProgram: PublicKey;
        in: PoolType[];
        out: PoolType[];
        mDecimals: number;
    };
};
interface PoolAccountInfoV4 {
    ammId: string;
    status: BN__default;
    baseDecimals: number;
    quoteDecimals: number;
    lpDecimals: number;
    baseReserve: BN__default;
    quoteReserve: BN__default;
    lpSupply: BN__default;
    startTime: BN__default;
}
interface ComputeAmountOutAmmLayout {
    amountIn: TransferAmountFee;
    amountOut: TransferAmountFee;
    minAmountOut: TransferAmountFee;
    currentPrice: Price | undefined;
    executionPrice: Price | null;
    priceImpact: Percent;
    fee: TokenAmountType[];
    routeType: 'amm';
    poolKey: PoolType[];
    remainingAccounts: PublicKey[][];
    poolReady: boolean;
    poolType: 'CLMM' | 'STABLE' | undefined;
    feeConfig?: {
        feeAmount: BN__default;
        feeAccount: PublicKey;
    };
    expirationTime: number | undefined;
    allTrade: boolean;
    slippage: Percent;
    clmmExPriceX64: (BN__default | undefined)[];
}
interface ComputeAmountOutRouteLayout {
    amountIn: TransferAmountFee;
    amountOut: TransferAmountFee;
    minAmountOut: TransferAmountFee;
    currentPrice: Price | undefined;
    executionPrice: Price | null;
    priceImpact: Percent;
    fee: TokenAmountType[];
    routeType: 'route';
    poolKey: PoolType[];
    remainingAccounts: (PublicKey[] | undefined)[];
    minMiddleAmountFee: TokenAmount | undefined;
    middleToken: Token;
    poolReady: boolean;
    poolType: ('CLMM' | 'STABLE' | undefined)[];
    feeConfig?: {
        feeAmount: BN__default;
        feeAccount: PublicKey;
    };
    expirationTime: number | undefined;
    allTrade: boolean;
    slippage: Percent;
    clmmExPriceX64: (BN__default | undefined)[];
}
type ComputeAmountOutLayout = ComputeAmountOutAmmLayout | ComputeAmountOutRouteLayout;
type makeSwapInstructionParam = {
    ownerInfo: {
        wallet: PublicKey;
        sourceToken: PublicKey;
        routeToken?: PublicKey;
        destinationToken: PublicKey;
    };
    inputMint: PublicKey;
    routeProgram: PublicKey;
    swapInfo: ComputeAmountOutLayout;
};
interface ReturnTypeGetAllRoute {
    directPath: PoolType[];
    addLiquidityPools: ApiPoolInfoItem[];
    routePathDict: RoutePathType;
    needSimulate: ApiPoolInfoItem[];
    needTickArray: ClmmPoolInfo[];
    needCheckToken: string[];
}
interface ReturnTypeFetchMultipleInfo {
    [ammId: string]: PoolAccountInfoV4;
}
type ReturnTypeGetAddLiquidityDefaultPool = ApiPoolInfoItem | undefined;
type ReturnTypeGetAllRouteComputeAmountOut = ComputeAmountOutLayout[];
declare class TradeV2 extends Base {
    static getAllRoute({ inputMint, outputMint, apiPoolList, clmmList, allowedRouteToken2022, }: {
        inputMint: PublicKey;
        outputMint: PublicKey;
        apiPoolList?: ApiPoolInfo;
        clmmList?: ClmmPoolInfo[];
        allowedRouteToken2022?: boolean;
    }): ReturnTypeGetAllRoute;
    static fetchMultipleInfo({ connection, pools, batchRequest, }: {
        connection: Connection;
        pools: ApiPoolInfoItem[];
        batchRequest?: boolean;
    }): Promise<ReturnTypeFetchMultipleInfo>;
    static getAddLiquidityDefaultPool({ addLiquidityPools, poolInfosCache, }: {
        addLiquidityPools: ApiPoolInfoItem[];
        poolInfosCache: {
            [ammId: string]: PoolAccountInfoV4;
        };
    }): ReturnTypeGetAddLiquidityDefaultPool;
    private static ComparePoolSize;
    static getAllRouteComputeAmountOut({ inputTokenAmount, outputToken, directPath, routePathDict, simulateCache, tickCache, mintInfos, slippage, chainTime, epochInfo, feeConfig, }: {
        directPath: PoolType[];
        routePathDict: RoutePathType;
        simulateCache: ReturnTypeFetchMultipleInfo;
        tickCache: ReturnTypeFetchMultiplePoolTickArrays;
        mintInfos: ReturnTypeFetchMultipleMintInfos;
        inputTokenAmount: TokenAmountType;
        outputToken: Token | Currency;
        slippage: Percent;
        chainTime: number;
        epochInfo: EpochInfo;
        feeConfig?: {
            feeBps: BN__default;
            feeAccount: PublicKey;
        };
    }): ReturnTypeGetAllRouteComputeAmountOut;
    private static computeAmountOut;
    static makeSwapInstruction({ routeProgram, ownerInfo, inputMint, swapInfo }: makeSwapInstructionParam): {
        address: {};
        innerTransaction: InnerTransaction;
    };
    static makeSwapInstructionSimple<T extends TxVersion>({ connection, swapInfo, ownerInfo, computeBudgetConfig, routeProgram, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        swapInfo: ComputeAmountOutLayout;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            associatedOnly: boolean;
            checkCreateATAOwner: boolean;
        };
        routeProgram: PublicKey;
        computeBudgetConfig?: ComputeBudgetConfig;
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
}

declare const MARKET_STATE_LAYOUT_V2: Structure<any, "", {
    [x: string]: any;
}>;
declare class MarketV2 extends Base {
    static makeCreateMarketInstructionSimple<T extends TxVersion>({ connection, wallet, baseInfo, quoteInfo, lotSize, // 1
    tickSize, // 0.01
    dexProgramId, makeTxVersion, lookupTableCache, requestQueueSpacce, eventQueueSpacce, orderbookQueueSpacce, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        wallet: PublicKey;
        baseInfo: {
            mint: PublicKey;
            decimals: number;
        };
        quoteInfo: {
            mint: PublicKey;
            decimals: number;
        };
        lotSize: number;
        tickSize: number;
        dexProgramId: PublicKey;
        requestQueueSpacce?: number;
        eventQueueSpacce?: number;
        orderbookQueueSpacce?: number;
    }): Promise<{
        address: {
            marketId: PublicKey;
            requestQueue: PublicKey;
            eventQueue: PublicKey;
            bids: PublicKey;
            asks: PublicKey;
            baseVault: PublicKey;
            quoteVault: PublicKey;
            baseMint: PublicKey;
            quoteMint: PublicKey;
        };
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeCreateMarketInstruction({ connection, wallet, marketInfo, }: {
        connection: Connection;
        wallet: PublicKey;
        marketInfo: {
            programId: PublicKey;
            id: {
                publicKey: PublicKey;
                seed: string;
            };
            baseMint: PublicKey;
            quoteMint: PublicKey;
            baseVault: {
                publicKey: PublicKey;
                seed: string;
            };
            quoteVault: {
                publicKey: PublicKey;
                seed: string;
            };
            vaultOwner: PublicKey;
            requestQueue: {
                publicKey: PublicKey;
                seed: string;
            };
            eventQueue: {
                publicKey: PublicKey;
                seed: string;
            };
            bids: {
                publicKey: PublicKey;
                seed: string;
            };
            asks: {
                publicKey: PublicKey;
                seed: string;
            };
            feeRateBps: number;
            vaultSignerNonce: BN__default;
            quoteDustThreshold: BN__default;
            baseLotSize: BN__default;
            quoteLotSize: BN__default;
            requestQueueSpacce?: number;
            eventQueueSpacce?: number;
            orderbookQueueSpacce?: number;
        };
    }): Promise<{
        address: {
            marketId: PublicKey;
            requestQueue: PublicKey;
            eventQueue: PublicKey;
            bids: PublicKey;
            asks: PublicKey;
            baseVault: PublicKey;
            quoteVault: PublicKey;
            baseMint: PublicKey;
            quoteMint: PublicKey;
        };
        innerTransactions: {
            instructions: TransactionInstruction[];
            signers: never[];
            instructionTypes: InstructionType[];
        }[];
    }>;
    static initializeMarketInstruction({ programId, marketInfo, }: {
        programId: PublicKey;
        marketInfo: {
            id: PublicKey;
            requestQueue: PublicKey;
            eventQueue: PublicKey;
            bids: PublicKey;
            asks: PublicKey;
            baseVault: PublicKey;
            quoteVault: PublicKey;
            baseMint: PublicKey;
            quoteMint: PublicKey;
            authority?: PublicKey;
            pruneAuthority?: PublicKey;
            baseLotSize: BN__default;
            quoteLotSize: BN__default;
            feeRateBps: number;
            vaultSignerNonce: BN__default;
            quoteDustThreshold: BN__default;
        };
    }): TransactionInstruction;
}

interface SHOW_INFO {
    programId: PublicKey;
    poolId: PublicKey;
    ammId: PublicKey;
    ownerAccountId: PublicKey;
    snapshotLpAmount: BN__default;
    openTime: number;
    endTime: number;
    project: (typeof Utils1216.VERSION_PROJECT)[number];
    canClaim: boolean;
    canClaimErrorType: canClaimErrorType;
    tokenInfo: {
        programId: PublicKey;
        mintAddress: PublicKey;
        mintVault: PublicKey;
        mintDecimals: number;
        perLpLoss: BN__default;
        debtAmount: BN__default;
    }[];
}
type canClaimErrorType = 'outOfOperationalTime' | 'alreadyClaimIt' | undefined;
declare class Utils1216 extends Base {
    static CLAIMED_NUM: number;
    static POOL_LAYOUT: Structure<number | PublicKey | Buffer | BN__default | BN__default[] | {
        mintDecimals: number;
        mintAddress: PublicKey;
        mintVault: PublicKey;
        perLpLoss: BN__default;
        totalClaimedAmount: BN__default;
    }[], "", {
        status: number;
        padding: BN__default[];
        openTime: BN__default;
        endTime: BN__default;
        bump: number;
        ammId: PublicKey;
        tokenInfo: {
            mintDecimals: number;
            mintAddress: PublicKey;
            mintVault: PublicKey;
            perLpLoss: BN__default;
            totalClaimedAmount: BN__default;
        }[];
    }>;
    static OWNER_LAYOUT: Structure<number | PublicKey | Buffer | BN__default | BN__default[] | {
        mintAddress: PublicKey;
        debtAmount: BN__default;
        claimedAmount: BN__default;
    }[], "", {
        owner: PublicKey;
        padding: BN__default[];
        poolId: PublicKey;
        bump: number;
        version: number;
        tokenInfo: {
            mintAddress: PublicKey;
            debtAmount: BN__default;
            claimedAmount: BN__default;
        }[];
        lpAmount: BN__default;
    }>;
    static DEFAULT_POOL_ID: PublicKey[];
    static SEED_CONFIG: {
        pool: {
            id: Buffer;
        };
        owner: {
            id: Buffer;
        };
    };
    static VERSION_PROJECT: readonly [undefined, "Francium", "Tulip", "Larix"];
    static getPdaPoolId(programId: PublicKey, ammId: PublicKey): {
        publicKey: PublicKey;
        nonce: number;
    };
    static getPdaOwnerId(programId: PublicKey, poolId: PublicKey, owner: PublicKey, version: number): {
        publicKey: PublicKey;
        nonce: number;
    };
    static getAllInfo({ connection, programId, poolIds, wallet, chainTime, }: {
        connection: Connection;
        programId: PublicKey;
        poolIds: PublicKey[];
        wallet: PublicKey;
        chainTime: number;
    }): Promise<SHOW_INFO[]>;
    static makeClaimInstructionSimple<T extends TxVersion>({ connection, poolInfo, ownerInfo, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfo: SHOW_INFO;
        ownerInfo: {
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            associatedOnly: boolean;
            checkCreateATAOwner: boolean;
        };
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeClaimAllInstructionSimple<T extends TxVersion>({ connection, poolInfos, ownerInfo, makeTxVersion, lookupTableCache, }: {
        makeTxVersion: T;
        lookupTableCache?: CacheLTA;
        connection: Connection;
        poolInfos: SHOW_INFO[];
        ownerInfo: {
            wallet: PublicKey;
            tokenAccounts: TokenAccount[];
            associatedOnly: boolean;
            checkCreateATAOwner: boolean;
        };
    }): Promise<{
        address: {};
        innerTransactions: (T extends TxVersion.LEGACY ? InnerSimpleLegacyTransaction : InnerSimpleV0Transaction)[];
    }>;
    static makeClaimInstruction({ programId, poolInfo, ownerInfo, }: {
        programId: PublicKey;
        poolInfo: SHOW_INFO;
        ownerInfo: {
            wallet: PublicKey;
            ownerPda: PublicKey;
            claimAddress: PublicKey[];
        };
    }): TransactionInstruction;
}

export { AMM_CONFIG_SEED, type API_LINK, AccountMeta, AccountMetaReadonly, AmmConfigLayout, type AmountSide, type ApiClmmConfig, type ApiClmmConfigItem, type ApiClmmPools, type ApiClmmPoolsItem, type ApiClmmPoolsItemStatistics, type ApiClmmPositionLine, type ApiClmmPositionLinePoint, type ApiFarmApr, type ApiFarmAprLine, type ApiFarmAprLinePoint, type ApiFarmAprV3, type ApiFarmAprV5, type ApiFarmAprV6, type ApiFarmInfo, type ApiFarmInfoV3, type ApiFarmInfoV5, type ApiFarmInfoV6, type ApiIdoInfo, type ApiIdoInfoItem, type ApiIdoProjectInfo, type ApiInfo, type ApiPairs, type ApiPairsItem, type ApiPoolInfo, type ApiPoolInfoItem, type ApiPoolInfoV4, type ApiPoolInfoV5, type ApiPrice, type ApiRpcs, type ApiRpcsItem, type ApiTime, type ApiTokenInfo, type ApiTokenInfoItem, type ApiVersion, BIT_PRECISION, BNDivCeil, BNLayout, Base, type BigNumberish, BitStructure, Blob, type CacheLTA, Clmm, type ClmmConfigInfo, type ClmmPoolInfo, type ClmmPoolPersonalPosition, type ClmmPoolRewardInfo, type ClmmPoolRewardLayoutInfo, type ComputeAmountOutAmmLayout, type ComputeAmountOutRouteLayout, type ComputeBudgetConfig, Currency, CurrencyAmount, DEVNET_FARM_POOLS, DEVNET_LP_TOKENS, DEVNET_OFFICIAL_LIQUIDITY_POOLS, DEVNET_PROGRAM_ID, DEVNET_SPL_TOKENS, DataElement, ENDPOINT, EXTENSION_TICKARRAY_BITMAP_SIZE, type EnumLayout, ErrorCode, type ExtensionKey, type Extensions, FARM_LEDGER_LAYOUT_V3_1, FARM_LEDGER_LAYOUT_V3_2, FARM_LEDGER_LAYOUT_V5_1, FARM_LEDGER_LAYOUT_V5_2, FARM_LEDGER_LAYOUT_V6_1, FARM_STATE_LAYOUT_V3, FARM_STATE_LAYOUT_V5, FARM_STATE_LAYOUT_V6, FARM_VERSION_TO_LEDGER_LAYOUT, FARM_VERSION_TO_STATE_LAYOUT, FEE_RATE_DENOMINATOR, FETCH_TICKARRAY_COUNT, FIVE, Farm, type FarmCreateAssociatedLedgerAccountInstructionParams, type FarmCreateInstructionParams, type FarmCreateInstructionParamsV6, type FarmCreatorAddRewardTokenInstructionParams, type FarmCreatorAddRewardTokenInstructionParamsV6, type FarmCreatorWithdrawRewardInstructionParams, type FarmCreatorWithdrawRewardInstructionParamsV6, type FarmCreatorWithdrawRewardInstructionSimpleParams, type FarmCreatorWithdrawRewardInstructionSimpleParamsV6, type FarmDepositInstructionParams, type FarmFetchMultipleInfoParams, type FarmFetchMultipleInfoReturn, type FarmFetchMultipleInfoReturnItem, type FarmLedger, type FarmLedgerLayout, type FarmLedgerLayoutV3_1, type FarmLedgerLayoutV3_2, type FarmLedgerLayoutV5_1, type FarmLedgerLayoutV5_2, type FarmLedgerLayoutV6_1, type FarmLedgerV3_1, type FarmLedgerV3_2, type FarmLedgerV5_1, type FarmLedgerV5_2, type FarmLedgerV6_1, type FarmPoolBaseInfo, type FarmPoolJsonInfo, type FarmPoolJsonInfoV1, type FarmPoolJsonInfoV3V4V5, type FarmPoolJsonInfoV6, type FarmPoolKeys, type FarmPoolsJsonFile, type FarmRestartInstructionParams, type FarmRestartInstructionParamsV6, type FarmRewardInfo, type FarmState, type FarmStateLayout, type FarmStateLayoutV3, type FarmStateLayoutV5, type FarmStateLayoutV6, type FarmStateV3, type FarmStateV5, type FarmStateV6, type FarmUserKeys, type FarmVersion, type FarmWithdrawInstructionParams, Fee, Fraction, type GetLayoutSchemaFromStructure, type GetMultipleAccountsInfoConfig, type GetStructureFromLayoutSchema, type GetStructureSchema, type GetTokenAccountsByOwnerConfig, type GetTransferAmountFee, type HandleTokenAccountParams, INSTRUCTION_PROGRAM_ID, type InnerSimpleLegacyTransaction, type InnerSimpleTransaction, type InnerSimpleV0Transaction, type InnerTransaction, InstructionType, type JsonFileMetaData, LIQUIDITY_FEES_DENOMINATOR, LIQUIDITY_FEES_NUMERATOR, LIQUIDITY_STATE_LAYOUT_V4, LIQUIDITY_STATE_LAYOUT_V5, LIQUIDITY_VERSION_TO_STATE_LAYOUT, LOG_B_2_X32, LOG_B_P_ERR_MARGIN_LOWER_X64, LOG_B_P_ERR_MARGIN_UPPER_X64, LOOKUP_TABLE_CACHE, Layout, type LayoutConstructor, Liquidity, type LiquidityAddInstructionParams, type LiquidityAddInstructionParamsV4, type LiquidityAddInstructionSimpleParams, type LiquidityAssociatedPoolKeys, type LiquidityAssociatedPoolKeysV4, type LiquidityComputeAmountInParams, type LiquidityComputeAmountOutParams, type LiquidityComputeAnotherAmountParams, type LiquidityFetchInfoParams, type LiquidityFetchMultipleInfoParams, type LiquidityInitPoolInstructionParams, type LiquidityInitPoolInstructionParamsV4, type LiquidityInitPoolTransactionParams, LiquidityMath, type LiquidityPoolBaseInfo, type LiquidityPoolInfo, type LiquidityPoolJsonInfo, type LiquidityPoolKeys, type LiquidityPoolKeysV4, LiquidityPoolStatus, type LiquidityPoolsJsonFile, type LiquidityRemoveInstructionParams, type LiquidityRemoveInstructionParamsV4, type LiquidityRemoveInstructionSimpleParams, type LiquiditySide, type LiquidityState, type LiquidityStateLayout, type LiquidityStateLayoutV4, type LiquidityStateLayoutV5, type LiquidityStateV4, type LiquidityStateV5, type LiquiditySwapFixedInInstructionParamsV4, type LiquiditySwapFixedOutInstructionParamsV4, type LiquiditySwapInstructionParams, type LiquiditySwapInstructionSimpleParams, type LiquidityUserKeys, type LiquidityVersion, LogLevel, Logger, type LpTokenInfo, type LpTokenJsonInfo, type LpTokens, type LpTokensJsonInfo, MAINNET_FARM_POOLS, MAINNET_LP_TOKENS, MAINNET_OFFICIAL_LIQUIDITY_POOLS, MAINNET_PROGRAM_ID, MAINNET_SPL_TOKENS, MARKET_STATE_LAYOUT_V2, MARKET_STATE_LAYOUT_V3, MARKET_VERSION_TO_STATE_LAYOUT, MAX_BASE64_SIZE, MAX_SQRT_PRICE_X64, MAX_TICK, MEMO_PROGRAM_ID, METADATA_PROGRAM_ID, MIN_SQRT_PRICE_X64, MIN_TICK, type MakeCreateFarmInstructionParamsV6, type MakeCreateFarmInstructionParamsV6Simple, type MakeInstructionOutType, Market, type MarketState, type MarketStateLayout, type MarketStateLayoutV3, type MarketStateV3, MarketV2, MathUtil, MaxU64, MaxUint128, type MintInfo, ModelDataInfo, ModelDataPubkey, NEGATIVE_ONE, type NativeTokenInfo, ONE, OPERATION_SEED, ObservationInfoLayout, ObservationLayout, OperationLayout, OptionLayout, POOL_REWARD_VAULT_SEED, POOL_SEED, POOL_TICK_ARRAY_BITMAP_SEED, POOL_VAULT_SEED, POSITION_SEED, Percent, PoolInfoLayout, type PoolType, PoolUtils, type PoolVars, PositionInfoLayout, PositionRewardInfoLayout, PositionUtils, Price, type ProgramId, ProtocolPositionLayout, type PublicKeyish, Q128, Q64, RAYDIUM_MAINNET, REAL_FARM_STATE_LAYOUT_V3, REAL_FARM_STATE_LAYOUT_V5, REAL_FARM_STATE_LAYOUT_V6, RENT_PROGRAM_ID, type ReplaceType, type ReturnTypeComputeAmountOut, type ReturnTypeComputeAmountOutBaseOut, type ReturnTypeComputeAmountOutFormat, type ReturnTypeFetchExBitmaps, type ReturnTypeFetchMultipleInfo, type ReturnTypeFetchMultipleMintInfo, type ReturnTypeFetchMultipleMintInfos, type ReturnTypeFetchMultiplePoolInfos, type ReturnTypeFetchMultiplePoolTickArrays, type ReturnTypeGetAddLiquidityDefaultPool, type ReturnTypeGetAllRoute, type ReturnTypeGetAllRouteComputeAmountOut, type ReturnTypeGetLiquidityAmountOut, type ReturnTypeGetPriceAndTick, type ReturnTypeGetTickPrice, RewardInfo, Rounding, SERUM_PROGRAMID_TO_VERSION, SERUM_PROGRAM_ID_V3, SERUM_VERSION_TO_PROGRAMID, type SHOW_INFO, SOL, SPL_ACCOUNT_LAYOUT, SPL_MINT_LAYOUT, SYSTEM_PROGRAM_ID, type SelectOrCreateTokenAccountParams, type SelectTokenAccountParams, type SerumVersion, Spl, type SplAccount, type SplAccountLayout, type SplMint, type SplMintLayout, type SplTokenInfo, type SplTokenJsonInfo, type SplTokens, type SplTokensJsonInfo, SqrtPriceMath, type StableModelLayout, type StepComputations, Structure, SwapMath, type SwapSide, TEN, TESTNET_FARM_POOLS, TESTNET_LP_TOKENS, TESTNET_OFFICIAL_LIQUIDITY_POOLS, TESTNET_SPL_TOKENS, THREE, TICK_ARRAY_BITMAP_SIZE, TICK_ARRAY_SEED, TICK_ARRAY_SIZE, TICK_SPACINGS, TWO, type Tick, type TickArray, TickArrayBitmap, TickArrayBitmapExtension, TickArrayBitmapExtensionLayout, type TickArrayBitmapExtensionType, TickArrayLayout, type TickArrayState, TickLayout, TickMath, TickQuery, type TickState, TickUtils, Token, type TokenAccount, TokenAmount, type TokenAmountType, TokenList, type TokensJsonFile, TradeV2, type TransferAmountFee, TxVersion, U64Resolution, U64_IGNORE_RANGE, UInt, Union, type UnsignedTransactionAndSigners, Utils1216, Voter, VoterDepositEntry, VoterLockup, VoterRegistrar, VoterVotingMintConfig, WSOL, WideBits, type Without, type WrappedBig, type WrappedDecimal, WrappedLayout, type XOR, ZERO, _100, _1000, _10000, _100_PERCENT, _SERUM_PROGRAM_ID_V3, array, bits, blob, bool, buildSimpleTransaction, buildTransaction, type canClaimErrorType, chunkArray, closePositionInstruction, collectRewardInstruction, createPoolInstruction, cstr, currencyEquals, decodeBool, decreaseLiquidityInstruction, divCeil, encodeBool, f32, f32be, f64, f64be, fetchMultipleMintInfos, findProgramAddress, forecastTransactionSize, formatLayout, generatePubKey, getDxByDyBaseIn, getDyByDxBaseIn, getMultipleAccountsInfo, getMultipleAccountsInfoWithCustomFlags, getMultipleLookupTableInfo, getPdaAmmConfigId, getPdaExBitmapAccount, getPdaMetadataKey, getPdaOperationAccount, getPdaPersonalPositionAddress, getPdaPoolId, getPdaPoolRewardVaulId, getPdaPoolVaultId, getPdaProtocolPositionAddress, getPdaTickArrayAddress, getStablePrice, getTransferAmountFee, getWSOLAmount, greedy, i128, i16ToBytes, i32ToBytes, i64, i8, increasePositionFromBaseInstruction, increasePositionFromLiquidityInstruction, initRewardInstruction, initStableModelLayout, inspectAll, inspectBN, inspectCurrency, inspectPublicKey, inspectToken, intersection, isZero, jsonInfo2PoolKeys, leadingZeros, leastSignificantBit, type makeCreateFarmInstructionParams, type makeCreateFarmInstructionSimpleParams, minExpirationTime, mostSignificantBit, ns64, ns64be, nu64, nu64be, offset, openPositionFromBaseInstruction, openPositionFromLiquidityInstruction, option, parseBigNumberish, parseSimulateLogToJson, parseSimulateValue, poolKeys2JsonInfo, poolTypeV6, publicKey, rustEnum, s16, s16be, s24, s24be, s32, s32be, s40, s40be, s48, s48be, s8, seq, setRewardInstruction, simulateMultipleInstruction, simulateTransaction, splitNumber, splitTxAndSigners, str, struct, swapInstruction, tagged, tenExponentiate, trailingZeros, u128, u16, u16ToBytes, u16be, u24, u24be, u32, u32ToBytes, u32be, u40, u40be, u48, u48be, u64, u8, union, unionLayoutDiscriminator, uniq, unwarpSol, utf8, validateAndParsePublicKey, vec, vecU8, xor, zeros };
