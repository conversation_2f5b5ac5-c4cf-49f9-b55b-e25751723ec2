{"name": "bs58", "version": "5.0.0", "description": "Base 58 encoding / decoding", "keywords": ["base58", "bitcoin", "crypto", "crytography", "decode", "decoding", "encode", "encoding", "litecoin"], "license": "MIT", "devDependencies": {"standard": "^16.0.4", "tape": "^4.6.3"}, "repository": {"url": "https://github.com/cryptocoinjs/bs58", "type": "git"}, "files": ["index.js", "index.d.ts"], "main": "index.js", "types": "index.d.ts", "scripts": {"standard": "standard", "test": "npm run standard && npm run unit", "unit": "tape test/index.js"}, "dependencies": {"base-x": "^4.0.0"}}