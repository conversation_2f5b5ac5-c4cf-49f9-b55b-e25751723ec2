'use strict'

module.exports = {
  kAgent: Sym<PERSON>('agent'),
  kOptions: Symbol('options'),
  kFactory: Symbol('factory'),
  kDispatches: Symbol('dispatches'),
  kDispatchKey: Symbol('dispatch key'),
  kDefaultHeaders: Symbol('default headers'),
  kDefaultTrailers: Symbol('default trailers'),
  kContentLength: Symbol('content length'),
  kMockAgent: Symbol('mock agent'),
  kMockAgentSet: Symbol('mock agent set'),
  kMockAgentGet: Symbol('mock agent get'),
  kMockDispatch: Symbol('mock dispatch'),
  kClose: Symbol('close'),
  kOriginalClose: Symbol('original agent close'),
  kOriginalDispatch: Symbol('original dispatch'),
  kOrigin: Symbol('origin'),
  kIsMockActive: Symbol('is mock active'),
  kNetConnect: Symbol('net connect'),
  kGetNetConnect: Symbol('get net connect'),
  kConnected: Symbol('connected'),
  kIgnoreTrailingSlash: Symbol('ignore trailing slash'),
  kMockAgentMockCallHistoryInstance: Symbol('mock agent mock call history name'),
  kMockAgentRegisterCallHistory: Symbol('mock agent register mock call history'),
  kMockAgentAddCallHistoryLog: Symbol('mock agent add call history log'),
  kMockAgentIsCallHistoryEnabled: Symbol('mock agent is call history enabled'),
  kMockAgentAcceptsNonStandardSearchParameters: Symbol('mock agent accepts non standard search parameters'),
  kMockCallHistoryAddLog: Symbol('mock call history add log')
}
