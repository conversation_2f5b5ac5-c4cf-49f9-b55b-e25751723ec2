import Dispatcher from './dispatcher'

export declare class Redirect<PERSON><PERSON>ler implements Di<PERSON>atcher.DispatchHandler {
  constructor (
    dispatch: Dispatcher.Dispatch,
    maxRedirections: number,
    opts: Dispatcher.DispatchOptions,
    handler: Dispatcher.DispatchHandler,
    redirectionLimitReached: boolean
  )
}

export declare class Decorator<PERSON><PERSON>ler implements Dispatcher.DispatchHandler {
  constructor (handler: Dispatcher.DispatchHandler)
}
