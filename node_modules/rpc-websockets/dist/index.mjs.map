{"version": 3, "sources": ["../src/lib/client/websocket.ts", "../src/lib/utils.ts", "../src/lib/client.ts", "../src/lib/server.ts", "../src/index.ts"], "names": ["EventEmitter", "uuidv1", "response"], "mappings": ";;;;;;AAeO,SAAS,SAAA,CACZ,SACA,OAEJ,EAAA;AACI,EAAO,OAAA,IAAI,aAAc,CAAA,OAAA,EAAS,OAAO,CAAA;AAC7C;;;ACXO,IAAM,kBAAN,MACP;AAAA,EACI,OAAO,KACP,EAAA;AACI,IAAO,OAAA,IAAA,CAAK,UAAU,KAAK,CAAA;AAAA;AAC/B,EAEA,OAAO,KACP,EAAA;AACI,IAAO,OAAA,IAAA,CAAK,MAAM,KAAK,CAAA;AAAA;AAE/B;;;ACea,IAAA,YAAA,GAAN,cAA2B,YAClC,CAAA;AAAA,EACY,OAAA;AAAA,EACA,MAAA;AAAA,EACA,KAAA;AAAA,EACA,OAAA;AAAA,EACA,WAAA;AAAA,EACA,KAAA;AAAA,EACA,SAAA;AAAA,EACA,kBAAA;AAAA,EACA,kBAAA;AAAA,EACA,cAAA;AAAA,EACA,YAAA;AAAA,EAEA,kBAAA;AAAA,EACA,mBAAA;AAAA,EAIA,MAAA;AAAA,EACA,gBAAA;AAAA,EACA,QAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYR,WAAA,CACI,gBACA,EAAA,OAAA,GAAU,qBACV,EAAA;AAAA,IACI,WAAc,GAAA,IAAA;AAAA,IACd,SAAY,GAAA,IAAA;AAAA,IACZ,kBAAqB,GAAA,GAAA;AAAA,IACrB,cAAiB,GAAA,CAAA;AAAA,IACjB,GAAG;AAAA,GACH,GAAA,EACJ,EAAA,mBAAA,EAIA,QAEJ,EAAA;AACI,IAAM,KAAA,EAAA;AAEN,IAAA,IAAA,CAAK,gBAAmB,GAAA,gBAAA;AAExB,IAAA,IAAA,CAAK,QAAQ,EAAC;AACd,IAAA,IAAA,CAAK,MAAS,GAAA,CAAA;AAEd,IAAA,IAAA,CAAK,OAAU,GAAA,OAAA;AACf,IAAA,IAAA,CAAK,WAAc,GAAA,WAAA;AACnB,IAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;AACb,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AACjB,IAAA,IAAA,CAAK,kBAAqB,GAAA,MAAA;AAC1B,IAAA,IAAA,CAAK,kBAAqB,GAAA,kBAAA;AAC1B,IAAA,IAAA,CAAK,cAAiB,GAAA,cAAA;AACtB,IAAA,IAAA,CAAK,YAAe,GAAA,YAAA;AACpB,IAAA,IAAA,CAAK,kBAAqB,GAAA,CAAA;AAC1B,IAAA,IAAA,CAAK,mBAAsB,GAAA,mBAAA,KAAwB,MAAM,OAAO,IAAK,CAAA,MAAA,KAAW,QAC1E,GAAA,EAAE,IAAK,CAAA,MAAA,GACP,MAAO,CAAA,IAAA,CAAK,MAAM,CAAI,GAAA,CAAA,CAAA;AAE5B,IAAA,IAAI,CAAC,QAAA,EAAe,IAAA,CAAA,QAAA,GAAW,IAAI,eAAgB,EAAA;AAAA,cACzC,QAAW,GAAA,QAAA;AAErB,IAAA,IAAI,IAAK,CAAA,WAAA;AACL,MAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAS,EAAA;AAAA,QACxB,aAAa,IAAK,CAAA,WAAA;AAAA,QAClB,WAAW,IAAK,CAAA,SAAA;AAAA,QAChB,oBAAoB,IAAK,CAAA,kBAAA;AAAA,QACzB,gBAAgB,IAAK,CAAA,cAAA;AAAA,QACrB,GAAG,IAAK,CAAA;AAAA,OACX,CAAA;AAAA;AACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OACA,GAAA;AACI,IAAA,IAAI,KAAK,MAAQ,EAAA;AAEjB,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAS,EAAA;AAAA,MACxB,aAAa,IAAK,CAAA,WAAA;AAAA,MAClB,WAAW,IAAK,CAAA,SAAA;AAAA,MAChB,oBAAoB,IAAK,CAAA,kBAAA;AAAA,MACzB,gBAAgB,IAAK,CAAA,cAAA;AAAA,MACrB,GAAG,IAAK,CAAA;AAAA,KACX,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IACI,CAAA,MAAA,EACA,MACA,EAAA,OAAA,EACA,OAEJ,EAAA;AACI,IAAA,IAAI,CAAC,OAAA,IAAW,QAAa,KAAA,OAAO,OACpC,EAAA;AACI,MAAU,OAAA,GAAA,OAAA;AACV,MAAU,OAAA,GAAA,IAAA;AAAA;AAGd,IAAA,OAAO,IAAI,OAAA,CAAQ,CAAC,OAAA,EAAS,MAC7B,KAAA;AACI,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA,OAAO,OAAO,IAAI,KAAA,CAAM,kBAAkB,CAAC,CAAA;AAE5D,MAAA,MAAM,MAAS,GAAA,IAAA,CAAK,mBAAoB,CAAA,MAAA,EAAQ,MAAM,CAAA;AAEtD,MAAA,MAAM,OAAU,GAAA;AAAA,QACZ,OAAS,EAAA,KAAA;AAAA,QACT,MAAA;AAAA,QACA,QAAQ,MAAU,IAAA,MAAA;AAAA,QAClB,EAAI,EAAA;AAAA,OACR;AAEA,MAAK,IAAA,CAAA,MAAA,CAAO,KAAK,IAAK,CAAA,QAAA,CAAS,OAAO,OAAO,CAAA,EAAG,OAAS,EAAA,CAAC,KAC1D,KAAA;AACI,QAAI,IAAA,KAAA,EAAc,OAAA,MAAA,CAAO,KAAK,CAAA;AAE9B,QAAK,IAAA,CAAA,KAAA,CAAM,MAAM,CAAI,GAAA,EAAE,SAAS,CAAC,OAAA,EAAS,MAAM,CAAE,EAAA;AAElD,QAAA,IAAI,OACJ,EAAA;AACI,UAAA,IAAA,CAAK,KAAM,CAAA,MAAM,CAAE,CAAA,OAAA,GAAU,WAAW,MACxC;AACI,YAAO,OAAA,IAAA,CAAK,MAAM,MAAM,CAAA;AACxB,YAAO,MAAA,CAAA,IAAI,KAAM,CAAA,eAAe,CAAC,CAAA;AAAA,aAClC,OAAO,CAAA;AAAA;AACd,OACH,CAAA;AAAA,KACJ,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM,MACZ,EAAA;AACI,IAAA,MAAM,IAAO,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,aAAa,MAAM,CAAA;AAEhD,IAAA,IAAI,CAAC,IAAA,EAAY,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAElD,IAAO,OAAA,IAAA;AAAA;AACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WACN,GAAA;AACI,IAAO,OAAA,MAAM,IAAK,CAAA,IAAA,CAAK,eAAe,CAAA;AAAA;AAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAA,CAAO,QAAgB,MACvB,EAAA;AACI,IAAA,OAAO,IAAI,OAAA,CAAc,CAAC,OAAA,EAAS,MACnC,KAAA;AACI,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA,OAAO,OAAO,IAAI,KAAA,CAAM,kBAAkB,CAAC,CAAA;AAE5D,MAAA,MAAM,OAAU,GAAA;AAAA,QACZ,OAAS,EAAA,KAAA;AAAA,QACT,MAAA;AAAA,QACA;AAAA,OACJ;AAEA,MAAK,IAAA,CAAA,MAAA,CAAO,KAAK,IAAK,CAAA,QAAA,CAAS,OAAO,OAAO,CAAA,EAAG,CAAC,KACjD,KAAA;AACI,QAAI,IAAA,KAAA,EAAc,OAAA,MAAA,CAAO,KAAK,CAAA;AAE9B,QAAQ,OAAA,EAAA;AAAA,OACX,CAAA;AAAA,KACJ,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,UAAU,KAChB,EAAA;AACI,IAAA,IAAI,OAAO,KAAA,KAAU,QAAU,EAAA,KAAA,GAAQ,CAAC,KAAK,CAAA;AAE7C,IAAA,MAAM,MAAS,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,UAAU,KAAK,CAAA;AAE9C,IAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,MAAA,CAAO,KAAK,CAAM,KAAA,IAAA;AAC/C,MAAA,MAAM,IAAI,KAAA;AAAA,QACN,kCAAqC,GAAA,KAAA,GAAQ,UAAa,GAAA,MAAA,CAAO,KAAK;AAAA,OAC1E;AAEJ,IAAO,OAAA,MAAA;AAAA;AACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,KAClB,EAAA;AACI,IAAA,IAAI,OAAO,KAAA,KAAU,QAAU,EAAA,KAAA,GAAQ,CAAC,KAAK,CAAA;AAE7C,IAAA,MAAM,MAAS,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,WAAW,KAAK,CAAA;AAE/C,IAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,MAAA,CAAO,KAAK,CAAM,KAAA,IAAA;AAC/C,MAAM,MAAA,IAAI,KAAM,CAAA,2CAAA,GAA8C,MAAM,CAAA;AAExE,IAAO,OAAA,MAAA;AAAA;AACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,KAAA,CAAM,MAAe,IACrB,EAAA;AACI,IAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,IAAQ,IAAA,GAAA,EAAM,IAAI,CAAA;AAAA;AACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,SACjB,EAAA;AACI,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,QACrB,EAAA;AACI,IAAA,IAAA,CAAK,kBAAqB,GAAA,QAAA;AAAA;AAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,cACjB,EAAA;AACI,IAAA,IAAA,CAAK,cAAiB,GAAA,cAAA;AAAA;AAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,QAAA,CACJ,SACA,OAEJ,EAAA;AACI,IAAA,YAAA,CAAa,KAAK,kBAAkB,CAAA;AACpC,IAAA,IAAA,CAAK,MAAS,GAAA,IAAA,CAAK,gBAAiB,CAAA,OAAA,EAAS,OAAO,CAAA;AAEpD,IAAK,IAAA,CAAA,MAAA,CAAO,gBAAiB,CAAA,MAAA,EAAQ,MACrC;AACI,MAAA,IAAA,CAAK,KAAQ,GAAA,IAAA;AACb,MAAA,IAAA,CAAK,KAAK,MAAM,CAAA;AAChB,MAAA,IAAA,CAAK,kBAAqB,GAAA,CAAA;AAAA,KAC7B,CAAA;AAED,IAAA,IAAA,CAAK,OAAO,gBAAiB,CAAA,SAAA,EAAW,CAAC,EAAE,IAAA,EAAM,SACjD,KAAA;AACI,MAAA,IAAI,OAAmB,YAAA,WAAA;AACnB,QAAA,OAAA,GAAU,MAAO,CAAA,IAAA,CAAK,OAAO,CAAA,CAAE,QAAS,EAAA;AAE5C,MACA,IAAA;AACI,QAAU,OAAA,GAAA,IAAA,CAAK,QAAS,CAAA,MAAA,CAAO,OAAO,CAAA;AAAA,eAEnC,KACP,EAAA;AACI,QAAA;AAAA;AAIJ,MAAA,IAAI,QAAQ,YAAgB,IAAA,IAAA,CAAK,UAAU,OAAQ,CAAA,YAAY,EAAE,MACjE,EAAA;AACI,QAAA,IAAI,CAAC,MAAA,CAAO,IAAK,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,MAAA;AAC7B,UAAO,OAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,YAAY,CAAA;AAEzC,QAAM,MAAA,IAAA,GAAO,CAAC,OAAA,CAAQ,YAAY,CAAA;AAElC,QAAA,IAAI,QAAQ,MAAO,CAAA,WAAA,KAAgB,QAAa,IAAA,CAAA,IAAA,CAAK,QAAQ,MAAM,CAAA;AAAA;AAG/D,UAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,OAAA,CAAQ,OAAO,MAAQ,EAAA,CAAA,EAAA;AACvC,YAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,MAAO,CAAA,CAAC,CAAC,CAAA;AAInC,QAAA,OAAO,OAAQ,CAAA,OAAA,EAAU,CAAA,IAAA,CAAK,MAC9B;AAEI,UAAK,IAAA,CAAA,IAAA,CAAK,KAAM,CAAA,IAAA,EAAM,IAAI,CAAA;AAAA,SAC7B,CAAA;AAAA;AAGL,MAAA,IAAI,CAAC,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAC1B,EAAA;AAEI,QAAA,IAAI,QAAQ,MACZ,EAAA;AAEI,UAAA,OAAO,OAAQ,CAAA,OAAA,EAAU,CAAA,IAAA,CAAK,MAC9B;AACI,YAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,MAAQ,EAAA,OAAA,EAAS,MAAM,CAAA;AAAA,WAC5C,CAAA;AAAA;AAGL,QAAA;AAAA;AAIJ,MAAI,IAAA,OAAA,IAAW,YAAY,QAAY,IAAA,OAAA;AACnC,QAAA,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAA,CAAE,QAAQ,CAAC,CAAA;AAAA,UAC5B,IAAI,KAAA;AAAA,YACA;AAAA;AAEJ,SACJ;AAEJ,MAAA,IAAI,IAAK,CAAA,KAAA,CAAM,OAAQ,CAAA,EAAE,CAAE,CAAA,OAAA;AACvB,QAAA,YAAA,CAAa,IAAK,CAAA,KAAA,CAAM,OAAQ,CAAA,EAAE,EAAE,OAAO,CAAA;AAE/C,MAAI,IAAA,OAAA,CAAQ,KAAO,EAAA,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAA,CAAE,OAAQ,CAAA,CAAC,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,WAC7D,IAAA,CAAK,MAAM,OAAQ,CAAA,EAAE,EAAE,OAAQ,CAAA,CAAC,CAAE,CAAA,OAAA,CAAQ,MAAM,CAAA;AAErD,MAAO,OAAA,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAA;AAAA,KAC/B,CAAA;AAED,IAAK,IAAA,CAAA,MAAA,CAAO,iBAAiB,OAAS,EAAA,CAAC,UAAU,IAAK,CAAA,IAAA,CAAK,OAAS,EAAA,KAAK,CAAC,CAAA;AAE1E,IAAA,IAAA,CAAK,OAAO,gBAAiB,CAAA,OAAA,EAAS,CAAC,EAAE,IAAA,EAAM,QAC/C,KAAA;AACI,MAAA,IAAI,IAAK,CAAA,KAAA;AAEL,QAAA,UAAA,CAAW,MAAM,IAAK,CAAA,IAAA,CAAK,SAAS,IAAM,EAAA,MAAM,GAAG,CAAC,CAAA;AAExD,MAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;AACb,MAAA,IAAA,CAAK,MAAS,GAAA,MAAA;AAEd,MAAA,IAAI,SAAS,GAAM,EAAA;AAEnB,MAAK,IAAA,CAAA,kBAAA,EAAA;AAEL,MAAA,IACI,KAAK,SACZ,KAAA,IAAA,CAAK,iBAAiB,IAAK,CAAA,kBAAA,IAC1B,KAAK,cAAmB,KAAA,CAAA,CAAA;AAElB,QAAA,IAAA,CAAK,kBAAqB,GAAA,UAAA;AAAA,UACtB,MAAM,IAAA,CAAK,QAAS,CAAA,OAAA,EAAS,OAAO,CAAA;AAAA,UACpC,IAAK,CAAA;AAAA,SACT;AAAA,KACP,CAAA;AAAA;AAET;AC9Xa,IAAA,MAAA,GAAN,cAAqBA,YAC5B,CAAA;AAAA,EACY,UAAA;AAAA,EACA,QAAA;AAAA,EACR,GAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAA,CACI,SACA,QAEJ,EAAA;AACI,IAAM,KAAA,EAAA;AAaN,IAAA,IAAA,CAAK,aAAa,EAAC;AAEnB,IAAA,IAAI,CAAC,QAAA,EAAe,IAAA,CAAA,QAAA,GAAW,IAAI,eAAgB,EAAA;AAAA,cACzC,QAAW,GAAA,QAAA;AAErB,IAAK,IAAA,CAAA,GAAA,GAAM,IAAI,eAAA,CAAgB,OAAO,CAAA;AAEtC,IAAA,IAAA,CAAK,IAAI,EAAG,CAAA,WAAA,EAAa,MAAM,IAAK,CAAA,IAAA,CAAK,WAAW,CAAC,CAAA;AAErD,IAAA,IAAA,CAAK,GAAI,CAAA,EAAA,CAAG,YAAc,EAAA,CAAC,QAA0B,OACrD,KAAA;AACI,MAAA,MAAM,CAAI,GAAA,GAAA,CAAI,KAAM,CAAA,OAAA,CAAQ,KAAK,IAAI,CAAA;AACrC,MAAA,MAAM,KAAK,CAAE,CAAA,QAAA;AAEb,MAAA,IAAI,EAAE,KAAM,CAAA,SAAA,EAAkB,MAAA,CAAA,GAAA,GAAM,EAAE,KAAM,CAAA,SAAA;AAAA,WACvC,MAAA,CAAO,MAAMC,EAAO,EAAA;AAGzB,MAAA,MAAA,CAAO,gBAAgB,CAAI,GAAA,KAAA;AAG3B,MAAO,MAAA,CAAA,EAAA,CAAG,SAAS,CAAC,KAAA,KAAU,KAAK,IAAK,CAAA,cAAA,EAAgB,MAAQ,EAAA,KAAK,CAAC,CAAA;AAGtE,MAAO,MAAA,CAAA,EAAA,CAAG,SAAS,MACnB;AACI,QAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,OAAQ,CAAA,MAAA,CAAO,OAAO,GAAG,CAAA;AAE7C,QAAW,KAAA,MAAA,KAAA,IAAS,OAAO,IAAK,CAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,MAAM,CAC1D,EAAA;AACI,UAAM,MAAA,KAAA,GAAQ,KAAK,UAAW,CAAA,EAAE,EAAE,MAAO,CAAA,KAAK,EAAE,OAAQ,CAAA,OAAA;AAAA,YACpD,MAAO,CAAA;AAAA,WACX;AAEA,UAAA,IAAI,KAAS,IAAA,CAAA;AACT,YAAK,IAAA,CAAA,UAAA,CAAW,EAAE,CAAE,CAAA,MAAA,CAAO,KAAK,CAAE,CAAA,OAAA,CAAQ,MAAO,CAAA,KAAA,EAAO,CAAC,CAAA;AAAA;AAGjE,QAAK,IAAA,CAAA,IAAA,CAAK,iBAAiB,MAAM,CAAA;AAAA,OACpC,CAAA;AAED,MAAA,IAAI,CAAC,IAAK,CAAA,UAAA,CAAW,EAAE,CAAG,EAAA,IAAA,CAAK,mBAAmB,EAAE,CAAA;AAGpD,MAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,QAAQ,GAAI,CAAA,MAAA,CAAO,KAAK,MAAM,CAAA;AAElD,MAAK,IAAA,CAAA,IAAA,CAAK,YAAc,EAAA,MAAA,EAAQ,OAAO,CAAA;AAEvC,MAAO,OAAA,IAAA,CAAK,UAAW,CAAA,MAAA,EAAQ,EAAE,CAAA;AAAA,KACpC,CAAA;AAED,IAAK,IAAA,CAAA,GAAA,CAAI,GAAG,OAAS,EAAA,CAAC,UAAU,IAAK,CAAA,IAAA,CAAK,OAAS,EAAA,KAAK,CAAC,CAAA;AAAA;AAC7D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,QACI,CAAA,IAAA,EACA,EACA,EAAA,EAAA,GAAK,GAET,EAAA;AACI,IAAA,IAAI,CAAC,IAAK,CAAA,UAAA,CAAW,EAAE,CAAG,EAAA,IAAA,CAAK,mBAAmB,EAAE,CAAA;AAEpD,IAAA,IAAA,CAAK,UAAW,CAAA,EAAE,CAAE,CAAA,WAAA,CAAY,IAAI,CAAI,GAAA;AAAA,MACpC,EAAA;AAAA,MACA,SAAW,EAAA;AAAA,KACf;AAEA,IAAO,OAAA;AAAA,MACH,SAAW,EAAA,MAAM,IAAK,CAAA,oBAAA,CAAqB,MAAM,EAAE,CAAA;AAAA,MACnD,MAAQ,EAAA,MAAM,IAAK,CAAA,iBAAA,CAAkB,MAAM,EAAE;AAAA,KACjD;AAAA;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,OAAA,CACI,EACA,EAAA,EAAA,GAAK,GAET,EAAA;AACI,IAAK,IAAA,CAAA,QAAA,CAAS,WAAa,EAAA,EAAA,EAAI,EAAE,CAAA;AAAA;AACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,oBAAA,CAAqB,IAAc,EAAA,EAAA,GAAK,GAChD,EAAA;AACI,IAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,WAAY,CAAA,IAAI,EAAE,SAAY,GAAA,IAAA;AAAA;AACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,iBAAA,CAAkB,IAAc,EAAA,EAAA,GAAK,GAC7C,EAAA;AACI,IAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,WAAY,CAAA,IAAI,EAAE,SAAY,GAAA,KAAA;AAAA;AACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,mBAAA,CAAoB,IAAc,EAAA,EAAA,GAAK,GAC/C,EAAA;AACI,IAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,MAAO,CAAA,IAAI,EAAE,SAAY,GAAA,IAAA;AAAA;AACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,gBAAA,CAAiB,IAAc,EAAA,EAAA,GAAK,GAC5C,EAAA;AACI,IAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,MAAO,CAAA,IAAI,EAAE,SAAY,GAAA,KAAA;AAAA;AACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,EACf,EAAA;AACI,IAAM,MAAA,SAAA,GAAY,IAAK,CAAA,UAAA,CAAW,EAAE,CAAA;AAEpC,IAAA,IAAI,SACJ,EAAA;AACI,MAAA,OAAO,SAAU,CAAA,WAAA;AACjB,MAAA,OAAO,SAAU,CAAA,MAAA;AAEjB,MAAA,KAAA,MAAW,UAAU,SAAU,CAAA,OAAA,CAAQ,MAAO,EAAA,SAAU,KAAM,EAAA;AAE9D,MAAO,OAAA,IAAA,CAAK,WAAW,EAAE,CAAA;AAAA;AAC7B;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAA,CAAM,IAAc,EAAA,EAAA,GAAK,GACzB,EAAA;AACI,IAAA,IAAI,CAAC,IAAK,CAAA,UAAA,CAAW,EAAE,CAAG,EAAA,IAAA,CAAK,mBAAmB,EAAE,CAAA;AAAA,SAEpD;AACI,MAAA,MAAM,QAAQ,IAAK,CAAA,UAAA,CAAW,EAAE,CAAA,CAAE,OAAO,IAAI,CAAA;AAE7C,MAAA,IAAI,KAAU,KAAA,MAAA;AACV,QAAA,MAAM,IAAI,KAAM,CAAA,CAAA,yBAAA,EAA4B,EAAE,CAAA,EAAG,IAAI,CAAE,CAAA,CAAA;AAAA;AAG/D,IAAA,IAAA,CAAK,UAAW,CAAA,EAAE,CAAE,CAAA,MAAA,CAAO,IAAI,CAAI,GAAA;AAAA,MAC/B,SAAS,EAAC;AAAA,MACV,SAAW,EAAA;AAAA,KACf;AAGA,IAAK,IAAA,CAAA,EAAA,CAAG,IAAM,EAAA,CAAA,GAAI,MAClB,KAAA;AAEI,MAAA,IAAI,MAAO,CAAA,MAAA,KAAW,CAAK,IAAA,MAAA,CAAO,CAAC,CAAa,YAAA,MAAA;AAC5C,QAAA,MAAA,GAAS,OAAO,CAAC,CAAA;AAErB,MAAW,KAAA,MAAA,SAAA,IAAa,KAAK,UAAW,CAAA,EAAE,EAAE,MAAO,CAAA,IAAI,EAAE,OACzD,EAAA;AACI,QAAA,MAAM,SAAS,IAAK,CAAA,UAAA,CAAW,EAAE,CAAE,CAAA,OAAA,CAAQ,IAAI,SAAS,CAAA;AAExD,QAAA,IAAI,CAAC,MAAQ,EAAA;AAEb,QAAO,MAAA,CAAA,IAAA;AAAA,UACH,IAAA,CAAK,SAAS,MAAO,CAAA;AAAA,YACjB,YAAc,EAAA,IAAA;AAAA,YACd;AAAA,WACH;AAAA,SACL;AAAA;AACJ,KACH,CAAA;AAED,IAAO,OAAA;AAAA,MACH,SAAW,EAAA,MAAM,IAAK,CAAA,mBAAA,CAAoB,MAAM,EAAE,CAAA;AAAA,MAClD,MAAQ,EAAA,MAAM,IAAK,CAAA,gBAAA,CAAiB,MAAM,EAAE;AAAA,KAChD;AAAA;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,GAAG,IACH,EAAA;AACI,IAAA,IAAI,CAAC,IAAK,CAAA,UAAA,CAAW,IAAI,CAAG,EAAA,IAAA,CAAK,mBAAmB,IAAI,CAAA;AAExD,IAAA,MAAM,IAAO,GAAA,IAAA;AAEb,IAAO,OAAA;AAAA;AAAA,MAEH,QAAA,CACI,SACA,EAEJ,EAAA;AACI,QAAA,IAAI,UAAU,MAAW,KAAA,CAAA;AACrB,UAAM,MAAA,IAAI,MAAM,oCAAoC,CAAA;AAExD,QAAA,IAAI,OAAO,OAAY,KAAA,QAAA;AACnB,UAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAE3C,QAAA,IAAI,OAAO,EAAO,KAAA,UAAA;AACd,UAAM,MAAA,IAAI,MAAM,4BAA4B,CAAA;AAEhD,QAAA,OAAO,IAAK,CAAA,QAAA,CAAS,OAAS,EAAA,EAAA,EAAI,IAAI,CAAA;AAAA,OAC1C;AAAA;AAAA,MAGA,MAAM,OACN,EAAA;AACI,QAAA,IAAI,UAAU,MAAW,KAAA,CAAA;AACrB,UAAM,MAAA,IAAI,MAAM,mCAAmC,CAAA;AAEvD,QAAA,IAAI,OAAO,OAAY,KAAA,QAAA;AACnB,UAAM,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAE3C,QAAO,OAAA,IAAA,CAAK,KAAM,CAAA,OAAA,EAAS,IAAI,CAAA;AAAA,OACnC;AAAA;AAAA,MAGA,IAAI,SACJ,GAAA;AACI,QAAA,OAAO,OAAO,IAAK,CAAA,IAAA,CAAK,UAAW,CAAA,IAAI,EAAE,MAAM,CAAA;AAAA,OACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAUA,IAAA,CAAK,UAAkB,MACvB,EAAA;AACI,QAAA,MAAM,UAAU,IAAK,CAAA,UAAA,CAAW,IAAI,CAAA,CAAE,OAAO,KAAK,CAAA;AAElD,QAAI,IAAA,OAAA;AACA,UAAW,KAAA,MAAA,SAAA,IAAa,QAAQ,OAChC,EAAA;AACI,YAAA,MAAM,SAAS,IAAK,CAAA,UAAA,CAAW,IAAI,CAAE,CAAA,OAAA,CAAQ,IAAI,SAAS,CAAA;AAE1D,YAAA,IAAI,CAAC,MAAQ,EAAA;AAEb,YAAO,MAAA,CAAA,IAAA;AAAA,cACH,IAAA,CAAK,SAAS,MAAO,CAAA;AAAA,gBACjB,YAAc,EAAA,KAAA;AAAA,gBACd;AAAA,eACH;AAAA,aACL;AAAA;AACJ,OACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASA,IAAI,IACJ,GAAA;AACI,QAAO,OAAA,IAAA;AAAA,OACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,SACA,GAAA;AACI,QAAM,MAAA,UAAA,GAAa,CAAC,GAAG,IAAA,CAAK,WAAW,IAAI,CAAA,CAAE,OAAQ,CAAA,IAAA,EAAM,CAAA;AAE3D,QAAA,OAAO,UAAW,CAAA,MAAA;AAAA,UACd,CAAC,KAAK,IAAU,MAAA;AAAA,YACZ,GAAG,GAAA;AAAA,YACH,CAAC,IAAI,GAAG,IAAA,CAAK,WAAW,IAAI,CAAA,CAAE,OAAQ,CAAA,GAAA,CAAI,IAAI;AAAA,WAClD,CAAA;AAAA,UACA;AAAC,SACL;AAAA,OACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQA,OACA,GAAA;AACI,QAAO,OAAA,IAAA,CAAK,WAAW,IAAI,CAAA;AAAA;AAC/B,KACJ;AAAA;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,SAAA,CAAU,KAAK,GACf,EAAA;AACI,IAAA,IAAI,CAAC,IAAK,CAAA,UAAA,CAAW,EAAE,CAAA,SAAU,EAAC;AAElC,IAAA,OAAO,OAAO,IAAK,CAAA,IAAA,CAAK,UAAW,CAAA,EAAE,EAAE,MAAM,CAAA;AAAA;AACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAA,CAAY,IAAc,EAAA,OAAA,EAAiB,IAC3C,EAAA;AACI,IAAO,OAAA;AAAA,MACH,IAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAM,IAAQ,IAAA;AAAA,KAClB;AAAA;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KACA,GAAA;AACI,IAAA,OAAO,IAAI,OAAA,CAAc,CAAC,OAAA,EAAS,MACnC,KAAA;AACI,MACA,IAAA;AACI,QAAA,IAAA,CAAK,IAAI,KAAM,EAAA;AACf,QAAA,IAAA,CAAK,KAAK,OAAO,CAAA;AACjB,QAAQ,OAAA,EAAA;AAAA,eAEL,KACP,EAAA;AACI,QAAA,MAAA,CAAO,KAAK,CAAA;AAAA;AAChB,KACH,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,UAAA,CAAW,MAA0B,EAAA,EAAA,GAAK,GAClD,EAAA;AACI,IAAO,MAAA,CAAA,EAAA,CAAG,SAAW,EAAA,OAAO,IAC5B,KAAA;AACI,MAAA,MAAM,cAAoD,EAAC;AAE3D,MAAA,IAAI,gBAAgB,WACpB,EAAA;AACI,QAAA,WAAA,CAAY,MAAS,GAAA,IAAA;AAErB,QAAA,IAAA,GAAO,MAAO,CAAA,IAAA,CAAK,IAAI,CAAA,CAAE,QAAS,EAAA;AAAA;AAGtC,MAAI,IAAA,MAAA,CAAO,eAAe,CAAG,EAAA;AAE7B,MAAI,IAAA,UAAA;AAEJ,MACA,IAAA;AACI,QAAa,UAAA,GAAA,IAAA,CAAK,QAAS,CAAA,MAAA,CAAO,IAAc,CAAA;AAAA,eAE7C,KACP,EAAA;AACI,QAAA,OAAO,MAAO,CAAA,IAAA;AAAA,UACV,IAAA,CAAK,SAAS,MAAO,CAAA;AAAA,YACjB,OAAS,EAAA,KAAA;AAAA,YACT,KAAO,EAAA,WAAA,CAAY,MAAQ,EAAA,KAAA,CAAM,UAAU,CAAA;AAAA,YAC3C,EAAI,EAAA;AAAA,WACP,CAAA;AAAA,UACD;AAAA,SACJ;AAAA;AAGJ,MAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,UAAU,CAC5B,EAAA;AACI,QAAA,IAAI,CAAC,UAAW,CAAA,MAAA;AACZ,UAAA,OAAO,MAAO,CAAA,IAAA;AAAA,YACV,IAAA,CAAK,SAAS,MAAO,CAAA;AAAA,cACjB,OAAS,EAAA,KAAA;AAAA,cACT,KAAA,EAAO,WAAY,CAAA,MAAA,EAAQ,eAAe,CAAA;AAAA,cAC1C,EAAI,EAAA;AAAA,aACP,CAAA;AAAA,YACD;AAAA,WACJ;AAEJ,QAAA,MAAM,YAAY,EAAC;AAEnB,QAAA,KAAA,MAAW,WAAW,UACtB,EAAA;AACI,UAAA,MAAMC,YAAW,MAAM,IAAA,CAAK,WAAW,OAAS,EAAA,MAAA,CAAO,KAAK,EAAE,CAAA;AAE9D,UAAA,IAAI,CAACA,SAAU,EAAA;AAEf,UAAA,SAAA,CAAU,KAAKA,SAAQ,CAAA;AAAA;AAG3B,QAAI,IAAA,CAAC,UAAU,MAAQ,EAAA;AAEvB,QAAA,OAAO,OAAO,IAAK,CAAA,IAAA,CAAK,SAAS,MAAO,CAAA,SAAS,GAAG,WAAW,CAAA;AAAA;AAGnE,MAAA,MAAM,WAAW,MAAM,IAAA,CAAK,WAAW,UAAY,EAAA,MAAA,CAAO,KAAK,EAAE,CAAA;AAEjE,MAAA,IAAI,CAAC,QAAU,EAAA;AAEf,MAAA,OAAO,OAAO,IAAK,CAAA,IAAA,CAAK,SAAS,MAAO,CAAA,QAAQ,GAAG,WAAW,CAAA;AAAA,KACjE,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,MAAc,UAAA,CAAW,OAAc,EAAA,SAAA,EAAmB,KAAK,GAC/D,EAAA;AACI,IAAI,IAAA,OAAO,OAAY,KAAA,QAAA,IAAY,OAAY,KAAA,IAAA;AAC3C,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,YAAY,MAAM,CAAA;AAAA,QACzB,EAAI,EAAA;AAAA,OACR;AAEJ,IAAA,IAAI,QAAQ,OAAY,KAAA,KAAA;AACpB,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,WAAY,CAAA,MAAA,EAAQ,0BAA0B,CAAA;AAAA,QACrD,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAEJ,IAAA,IAAI,CAAC,OAAQ,CAAA,MAAA;AACT,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,WAAY,CAAA,MAAA,EAAQ,sBAAsB,CAAA;AAAA,QACjD,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAEJ,IAAI,IAAA,OAAO,QAAQ,MAAW,KAAA,QAAA;AAC1B,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,WAAY,CAAA,MAAA,EAAQ,qBAAqB,CAAA;AAAA,QAChD,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAEJ,IAAA,IAAI,OAAQ,CAAA,MAAA,IAAU,OAAO,OAAA,CAAQ,MAAW,KAAA,QAAA;AAC5C,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,YAAY,MAAM,CAAA;AAAA,QACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAEJ,IAAI,IAAA,OAAA,CAAQ,WAAW,QACvB,EAAA;AACI,MAAA,IAAI,CAAC,OAAQ,CAAA,MAAA;AACT,QAAO,OAAA;AAAA,UACH,OAAS,EAAA,KAAA;AAAA,UACT,KAAA,EAAO,YAAY,KAAM,CAAA;AAAA,UACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,SACtB;AAEJ,MAAA,MAAM,UAA4B,EAAC;AAEnC,MAAA,MAAM,cAAc,MAAO,CAAA,IAAA,CAAK,KAAK,UAAW,CAAA,EAAE,EAAE,MAAM,CAAA;AAE1D,MAAW,KAAA,MAAA,IAAA,IAAQ,QAAQ,MAC3B,EAAA;AACI,QAAM,MAAA,KAAA,GAAQ,WAAY,CAAA,OAAA,CAAQ,IAAI,CAAA;AACtC,QAAM,MAAA,SAAA,GAAY,IAAK,CAAA,UAAA,CAAW,EAAE,CAAA;AAEpC,QAAA,IAAI,UAAU,EACd,EAAA;AACI,UAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,wBAAA;AAChB,UAAA;AAAA;AAIJ,QAAA,IACI,SAAU,CAAA,MAAA,CAAO,WAAY,CAAA,KAAK,CAAC,CAAE,CAAA,SAAA,KAAc,IAC7D,IAAA,SAAA,CAAU,QAAQ,GAAI,CAAA,SAAS,CAAE,CAAA,gBAAgB,MAAM,KAEjD,EAAA;AACI,UAAO,OAAA;AAAA,YACH,OAAS,EAAA,KAAA;AAAA,YACT,KAAA,EAAO,YAAY,MAAM,CAAA;AAAA,YACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,WACtB;AAAA;AAGJ,QAAM,MAAA,YAAA,GACZ,UAAU,MAAO,CAAA,WAAA,CAAY,KAAK,CAAC,CAAA,CAAE,OAAQ,CAAA,OAAA,CAAQ,SAAS,CAAA;AACxD,QAAA,IAAI,gBAAgB,CACpB,EAAA;AACI,UAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,6CAAA;AAChB,UAAA;AAAA;AAEJ,QAAA,SAAA,CAAU,OAAO,WAAY,CAAA,KAAK,CAAC,CAAE,CAAA,OAAA,CAAQ,KAAK,SAAS,CAAA;AAE3D,QAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,IAAA;AAAA;AAGpB,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,MAAQ,EAAA,OAAA;AAAA,QACR,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAAA,KACJ,MAAA,IACS,OAAQ,CAAA,MAAA,KAAW,SAC5B,EAAA;AACI,MAAA,IAAI,CAAC,OAAQ,CAAA,MAAA;AACT,QAAO,OAAA;AAAA,UACH,OAAS,EAAA,KAAA;AAAA,UACT,KAAA,EAAO,YAAY,KAAM,CAAA;AAAA,UACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,SACtB;AAEJ,MAAA,MAAM,UAAsB,EAAC;AAE7B,MAAW,KAAA,MAAA,IAAA,IAAQ,QAAQ,MAC3B,EAAA;AACI,QAAA,IAAI,CAAC,IAAK,CAAA,UAAA,CAAW,EAAE,CAAE,CAAA,MAAA,CAAO,IAAI,CACpC,EAAA;AACI,UAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,wBAAA;AAChB,UAAA;AAAA;AAGJ,QAAM,MAAA,KAAA,GACZ,IAAK,CAAA,UAAA,CAAW,EAAE,CAAA,CAAE,OAAO,IAAI,CAAA,CAAE,OAAQ,CAAA,OAAA,CAAQ,SAAS,CAAA;AAEpD,QAAA,IAAI,UAAU,EACd,EAAA;AACI,UAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,gBAAA;AAChB,UAAA;AAAA;AAGJ,QAAK,IAAA,CAAA,UAAA,CAAW,EAAE,CAAE,CAAA,MAAA,CAAO,IAAI,CAAE,CAAA,OAAA,CAAQ,MAAO,CAAA,KAAA,EAAO,CAAC,CAAA;AACxD,QAAA,OAAA,CAAQ,IAAI,CAAI,GAAA,IAAA;AAAA;AAGpB,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,MAAQ,EAAA,OAAA;AAAA,QACR,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAAA,KACJ,MAAA,IACS,OAAQ,CAAA,MAAA,KAAW,WAC5B,EAAA;AACI,MAAA,IAAI,CAAC,OAAQ,CAAA,MAAA;AACT,QAAO,OAAA;AAAA,UACH,OAAS,EAAA,KAAA;AAAA,UACT,KAAA,EAAO,YAAY,MAAM,CAAA;AAAA,UACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,SACtB;AAAA;AAGR,IAAI,IAAA,CAAC,KAAK,UAAW,CAAA,EAAE,EAAE,WAAY,CAAA,OAAA,CAAQ,MAAM,CACnD,EAAA;AACI,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,YAAY,MAAM,CAAA;AAAA,QACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAAA;AAGJ,IAAA,IAAI,QAAW,GAAA,IAAA;AAGf,IACI,IAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,YAAY,OAAQ,CAAA,MAAM,EAAE,SAAc,KAAA,IAAA,IACpE,KAAK,UAAW,CAAA,EAAE,EAAE,OAAQ,CAAA,GAAA,CAAI,SAAS,CAAE,CAAA,gBAAgB,MAAM,KAE/D,EAAA;AACI,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA,EAAO,YAAY,MAAM,CAAA;AAAA,QACzB,EAAA,EAAI,QAAQ,EAAM,IAAA;AAAA,OACtB;AAAA;AAGJ,IACA,IAAA;AACI,MAAW,QAAA,GAAA,MAAM,KAAK,UAAW,CAAA,EAAE,EAAE,WAAY,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,EAAA;AAAA,QAC7D,OAAQ,CAAA,MAAA;AAAA,QACR;AAAA,OACJ;AAAA,aAEG,KACP,EAAA;AACI,MAAI,IAAA,CAAC,QAAQ,EAAI,EAAA;AAEjB,MAAA,IAAI,KAAiB,YAAA,KAAA;AACjB,QAAO,OAAA;AAAA,UACH,OAAS,EAAA,KAAA;AAAA,UACT,KAAO,EAAA;AAAA,YACH,IAAM,EAAA,KAAA;AAAA,YACN,SAAS,KAAM,CAAA,IAAA;AAAA,YACf,MAAM,KAAM,CAAA;AAAA,WAChB;AAAA,UACA,IAAI,OAAQ,CAAA;AAAA,SAChB;AAEJ,MAAO,OAAA;AAAA,QACH,OAAS,EAAA,KAAA;AAAA,QACT,KAAA;AAAA,QACA,IAAI,OAAQ,CAAA;AAAA,OAChB;AAAA;AAIJ,IAAI,IAAA,CAAC,QAAQ,EAAI,EAAA;AAGjB,IAAA,IAAI,OAAQ,CAAA,MAAA,KAAW,WAAe,IAAA,QAAA,KAAa,IACnD,EAAA;AACI,MAAA,MAAM,IAAI,IAAK,CAAA,UAAA,CAAW,EAAE,CAAE,CAAA,OAAA,CAAQ,IAAI,SAAS,CAAA;AACnD,MAAA,CAAA,CAAE,gBAAgB,CAAI,GAAA,IAAA;AACtB,MAAA,IAAA,CAAK,WAAW,EAAE,CAAA,CAAE,OAAQ,CAAA,GAAA,CAAI,WAAW,CAAC,CAAA;AAAA;AAGhD,IAAO,OAAA;AAAA,MACH,OAAS,EAAA,KAAA;AAAA,MACT,MAAQ,EAAA,QAAA;AAAA,MACR,IAAI,OAAQ,CAAA;AAAA,KAChB;AAAA;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASQ,mBAAmB,IAC3B,EAAA;AACI,IAAK,IAAA,CAAA,UAAA,CAAW,IAAI,CAAI,GAAA;AAAA,MACpB,WAAa,EAAA;AAAA,QACT,aAAe,EAAA;AAAA,UACX,EAAA,EAAI,MAAM,MAAO,CAAA,IAAA,CAAK,KAAK,UAAW,CAAA,IAAI,EAAE,WAAW,CAAA;AAAA,UACvD,SAAW,EAAA;AAAA;AACf,OACJ;AAAA,MACA,OAAA,sBAAa,GAAI,EAAA;AAAA,MACjB,QAAQ;AAAC,KACb;AAAA;AAER;AAEA,IAAM,UAAA,uBAAiB,GAAI,CAAA;AAAA,EACvB,CAAC,OAAQ,oBAAoB,CAAA;AAAA,EAC7B,CAAC,QAAQ,iBAAiB,CAAA;AAAA,EAC1B,CAAC,QAAQ,kBAAkB,CAAA;AAAA,EAC3B,CAAC,QAAQ,gBAAgB,CAAA;AAAA,EACzB,CAAC,QAAQ,gBAAgB,CAAA;AAAA,EACzB,CAAC,QAAQ,kBAAkB,CAAA;AAAA,EAC3B,CAAC,QAAQ,kBAAkB,CAAA;AAAA,EAC3B,CAAC,QAAQ,iBAAiB,CAAA;AAAA,EAC1B,CAAC,QAAQ,aAAa;AAC1B,CAAC,CAAA;AAQM,SAAS,WAAA,CAAY,MAAc,OAC1C,EAAA;AACI,EAAA,MAAM,KAAmB,GAAA;AAAA,IACrB,IAAA;AAAA,IACA,OAAS,EAAA,UAAA,CAAW,GAAI,CAAA,IAAI,CAAK,IAAA;AAAA,GACrC;AAEA,EAAI,IAAA,OAAA,EAAe,KAAA,CAAA,MAAM,CAAI,GAAA,OAAA;AAE7B,EAAO,OAAA,KAAA;AACX;;;AC1zBa,IAAA,MAAA,GAAN,cAAqB,YAC5B,CAAA;AAAA,EACI,WAAA,CACI,UAAU,qBACV,EAAA;AAAA,IACI,WAAc,GAAA,IAAA;AAAA,IACd,SAAY,GAAA,IAAA;AAAA,IACZ,kBAAqB,GAAA,GAAA;AAAA,IACrB,cAAiB,GAAA,CAAA;AAAA,IACjB,GAAG;AAAA,GACP,GAA2D,EAAC,EAC5D,mBAKJ,EAAA;AACI,IAAA,KAAA;AAAA,MACF,SAAA;AAAA,MACA,OAAA;AAAA,MACA;AAAA,QACI,WAAA;AAAA,QACA,SAAA;AAAA,QACA,kBAAA;AAAA,QACA,cAAA;AAAA,QACA,GAAG;AAAA,OACP;AAAA,MACA;AAAA,KACE;AAAA;AAER", "file": "index.mjs", "sourcesContent": ["/* A wrapper for the \"qaap/uws-bindings\" library. */\n\n\"use strict\"\n\nimport WebSocketImpl from \"ws\"\n\nimport { IWSClientAdditionalOptions } from \"./client.types.js\"\n\n/**\n * factory method for common WebSocket instance\n * @method\n * @param {String} address - url to a websocket server\n * @param {(Object)} options - websocket options\n * @return {Undefined}\n */\nexport function WebSocket(\n    address: string,\n    options: IWSClientAdditionalOptions & WebSocketImpl.ClientOptions\n)\n{\n    return new WebSocketImpl(address, options)\n}\n", "\"use strict\"\n\nexport interface DataPack<\n  T,\n  R extends string | ArrayBufferLike | Blob | ArrayBufferView\n> {\n  encode(value: T): R;\n  decode(value: R): T;\n}\n\nexport class DefaultDataPack implements DataPack<Object, string>\n{\n    encode(value: Object): string\n    {\n        return JSON.stringify(value)\n    }\n\n    decode(value: string): Object\n    {\n        return JSON.parse(value)\n    }\n}\n", "/**\n * \"Client\" wraps \"ws\" or a browser-implemented \"WebSocket\" library\n * according to the environment providing JSON RPC 2.0 support on top.\n * @module Client\n */\n\n\"use strict\"\n\nimport NodeWebSocket from \"ws\"\nimport { EventEmitter } from \"eventemitter3\"\nimport {\n    ICommonWebSocket,\n    IWSClientAdditionalOptions,\n    NodeWebSocketType,\n    ICommonWebSocketFactory,\n} from \"./client/client.types.js\"\n\nimport { <PERSON>Pack, DefaultDataPack } from \"./utils.js\"\n\ninterface IQueueElement {\n  promise: [\n    Parameters<ConstructorParameters<typeof Promise>[0]>[0],\n    Parameters<ConstructorParameters<typeof Promise>[0]>[1]\n  ];\n  timeout?: ReturnType<typeof setTimeout>;\n}\n\nexport interface IQueue {\n  [x: number | string]: IQueueElement;\n}\n\nexport interface IWSRequestParams {\n  [x: string]: any;\n  [x: number]: any;\n}\n\nexport class CommonClient extends EventEmitter\n{\n    private address: string\n    private rpc_id: number | string\n    private queue: IQueue\n    private options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    private autoconnect: boolean\n    private ready: boolean\n    private reconnect: boolean\n    private reconnect_timer_id: NodeJS.Timeout\n    private reconnect_interval: number\n    private max_reconnects: number\n    private rest_options: IWSClientAdditionalOptions &\n    NodeWebSocket.ClientOptions\n    private current_reconnects: number\n    private generate_request_id: (\n    method: string,\n    params: object | Array<any>\n  ) => number | string\n    private socket: ICommonWebSocket\n    private webSocketFactory: ICommonWebSocketFactory\n    private dataPack: DataPack<object, string>\n\n    /**\n   * Instantiate a Client class.\n   * @constructor\n   * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n   * @param {String} address - url to a websocket server\n   * @param {Object} options - ws options object with reconnect parameters\n   * @param {Function} generate_request_id - custom generation request Id\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {CommonClient}\n   */\n    constructor(\n        webSocketFactory: ICommonWebSocketFactory,\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        } = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        this.webSocketFactory = webSocketFactory\n\n        this.queue = {}\n        this.rpc_id = 0\n\n        this.address = address\n        this.autoconnect = autoconnect\n        this.ready = false\n        this.reconnect = reconnect\n        this.reconnect_timer_id = undefined\n        this.reconnect_interval = reconnect_interval\n        this.max_reconnects = max_reconnects\n        this.rest_options = rest_options\n        this.current_reconnects = 0\n        this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\"\n            ? ++this.rpc_id\n            : Number(this.rpc_id) + 1)\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        if (this.autoconnect)\n            this._connect(this.address, {\n                autoconnect: this.autoconnect,\n                reconnect: this.reconnect,\n                reconnect_interval: this.reconnect_interval,\n                max_reconnects: this.max_reconnects,\n                ...this.rest_options,\n            })\n    }\n\n    /**\n   * Connects to a defined server if not connected already.\n   * @method\n   * @return {Undefined}\n   */\n    connect()\n    {\n        if (this.socket) return\n\n        this._connect(this.address, {\n            autoconnect: this.autoconnect,\n            reconnect: this.reconnect,\n            reconnect_interval: this.reconnect_interval,\n            max_reconnects: this.max_reconnects,\n            ...this.rest_options,\n        })\n    }\n\n    /**\n   * Calls a registered RPC method on server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object|Array} params - optional method parameters\n   * @param {Number} timeout - RPC reply timeout value\n   * @param {Object} ws_opts - options passed to ws\n   * @return {Promise}\n   */\n    call(\n        method: string,\n        params?: IWSRequestParams,\n        timeout?: number,\n        ws_opts?: Parameters<NodeWebSocketType[\"send\"]>[1]\n    )\n    {\n        if (!ws_opts && \"object\" === typeof timeout)\n        {\n            ws_opts = timeout\n            timeout = null\n        }\n\n        return new Promise((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const rpc_id = this.generate_request_id(method, params)\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params: params || undefined,\n                id: rpc_id,\n            }\n\n            this.socket.send(this.dataPack.encode(message), ws_opts, (error) =>\n            {\n                if (error) return reject(error)\n\n                this.queue[rpc_id] = { promise: [resolve, reject] }\n\n                if (timeout)\n                {\n                    this.queue[rpc_id].timeout = setTimeout(() =>\n                    {\n                        delete this.queue[rpc_id]\n                        reject(new Error(\"reply timeout\"))\n                    }, timeout)\n                }\n            })\n        })\n    }\n\n    /**\n   * Logins with the other side of the connection.\n   * @method\n   * @param {Object} params - Login credentials object\n   * @return {Promise}\n   */\n    async login(params: IWSRequestParams)\n    {\n        const resp = await this.call(\"rpc.login\", params)\n\n        if (!resp) throw new Error(\"authentication failed\")\n\n        return resp\n    }\n\n    /**\n   * Fetches a list of client's methods registered on server.\n   * @method\n   * @return {Array}\n   */\n    async listMethods()\n    {\n        return await this.call(\"__listMethods\")\n    }\n\n    /**\n   * Sends a JSON-RPC 2.0 notification to server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object} params - optional method parameters\n   * @return {Promise}\n   */\n    notify(method: string, params?: IWSRequestParams)\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params,\n            }\n\n            this.socket.send(this.dataPack.encode(message), (error) =>\n            {\n                if (error) return reject(error)\n\n                resolve()\n            })\n        })\n    }\n\n    /**\n   * Subscribes for a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async subscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.on\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\n                \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n            )\n\n        return result\n    }\n\n    /**\n   * Unsubscribes from a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async unsubscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.off\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\"Failed unsubscribing from an event with: \" + result)\n\n        return result\n    }\n\n    /**\n   * Closes a WebSocket connection gracefully.\n   * @method\n   * @param {Number} code - socket close code\n   * @param {String} data - optional data to be sent before closing\n   * @return {Undefined}\n   */\n    close(code?: number, data?: string)\n    {\n        this.socket.close(code || 1000, data)\n    }\n\n    /**\n   * Enable / disable automatic reconnection.\n   * @method\n   * @param {Boolean} reconnect - enable / disable reconnection\n   * @return {Undefined}\n   */\n    setAutoReconnect(reconnect: boolean)\n    {\n        this.reconnect = reconnect\n    }\n\n    /**\n   * Set the interval between reconnection attempts.\n   * @method\n   * @param {Number} interval - reconnection interval in milliseconds\n   * @return {Undefined}\n   */\n    setReconnectInterval(interval: number)\n    {\n        this.reconnect_interval = interval\n    }\n\n    /**\n   * Set the maximum number of reconnection attempts.\n   * @method\n   * @param {Number} max_reconnects - maximum reconnection attempts\n   * @return {Undefined}\n   */\n    setMaxReconnects(max_reconnects: number)\n    {\n        this.max_reconnects = max_reconnects\n    }\n\n    /**\n   * Connection/Message handler.\n   * @method\n   * @private\n   * @param {String} address - WebSocket API address\n   * @param {Object} options - ws options object\n   * @return {Undefined}\n   */\n    private _connect(\n        address: string,\n        options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    )\n    {\n        clearTimeout(this.reconnect_timer_id)\n        this.socket = this.webSocketFactory(address, options)\n\n        this.socket.addEventListener(\"open\", () =>\n        {\n            this.ready = true\n            this.emit(\"open\")\n            this.current_reconnects = 0\n        })\n\n        this.socket.addEventListener(\"message\", ({ data: message }) =>\n        {\n            if (message instanceof ArrayBuffer)\n                message = Buffer.from(message).toString()\n\n            try\n            {\n                message = this.dataPack.decode(message)\n            }\n            catch (error)\n            {\n                return\n            }\n\n            // check if any listeners are attached and forward event\n            if (message.notification && this.listeners(message.notification).length)\n            {\n                if (!Object.keys(message.params).length)\n                    return this.emit(message.notification)\n\n                const args = [message.notification]\n\n                if (message.params.constructor === Object) args.push(message.params)\n                // using for-loop instead of unshift/spread because performance is better\n                else\n                    for (let i = 0; i < message.params.length; i++)\n                        args.push(message.params[i])\n\n                // run as microtask so that pending queue messages are resolved first\n                // eslint-disable-next-line prefer-spread\n                return Promise.resolve().then(() =>\n                {\n                    // eslint-disable-next-line prefer-spread\n                    this.emit.apply(this, args)\n                })\n            }\n\n            if (!this.queue[message.id])\n            {\n                // general JSON RPC 2.0 events\n                if (message.method)\n                {\n                    // run as microtask so that pending queue messages are resolved first\n                    return Promise.resolve().then(() =>\n                    {\n                        this.emit(message.method, message?.params)\n                    })\n                }\n\n                return\n            }\n\n            // reject early since server's response is invalid\n            if (\"error\" in message === \"result\" in message)\n                this.queue[message.id].promise[1](\n                    new Error(\n                        \"Server response malformed. Response must include either \\\"result\\\"\" +\n              \" or \\\"error\\\", but not both.\"\n                    )\n                )\n\n            if (this.queue[message.id].timeout)\n                clearTimeout(this.queue[message.id].timeout)\n\n            if (message.error) this.queue[message.id].promise[1](message.error)\n            else this.queue[message.id].promise[0](message.result)\n\n            delete this.queue[message.id]\n        })\n\n        this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error))\n\n        this.socket.addEventListener(\"close\", ({ code, reason }) =>\n        {\n            if (this.ready)\n            // Delay close event until internal state is updated\n                setTimeout(() => this.emit(\"close\", code, reason), 0)\n\n            this.ready = false\n            this.socket = undefined\n\n            if (code === 1000) return\n\n            this.current_reconnects++\n\n            if (\n                this.reconnect &&\n        (this.max_reconnects > this.current_reconnects ||\n          this.max_reconnects === 0)\n            )\n                this.reconnect_timer_id = setTimeout(\n                    () => this._connect(address, options),\n                    this.reconnect_interval\n                )\n        })\n    }\n}\n", "/**\n * \"Server\" wraps the \"ws\" library providing JSON RPC 2.0 support on top.\n * @module Server\n */\n\n\"use strict\"\n\nimport { EventEmitter } from \"eventemitter3\"\nimport url from \"node:url\"\nimport { v1 as uuidv1 } from \"uuid\"\nimport NodeWebSocket, { WebSocketServer } from \"ws\"\n\nimport { DataPack, DefaultDataPack } from \"./utils.js\"\n\ninterface INamespaceEvent {\n  [x: string]: {\n    sockets: Array<string>;\n    protected: boolean;\n  };\n}\n\ninterface IMethod {\n  public: () => void;\n  protected: () => void;\n}\n\ninterface IEvent {\n  public: () => void;\n  protected: () => void;\n}\n\ninterface IRPCError {\n  code: number;\n  message: string;\n  data?: string;\n}\n\ninterface IRPCMethodParams {\n  [x: string]: any;\n}\n\ninterface IRPCMethod {\n  [x: string]: {\n    fn: (params: IRPCMethodParams, socket_id: string) => any;\n    protected: boolean;\n  };\n}\n\ninterface INamespace {\n  [x: string]: {\n    rpc_methods: IRPCMethod;\n    clients: Map<string, IClientWebSocket>;\n    events: INamespaceEvent;\n  };\n}\n\ninterface IClientWebSocket extends NodeWebSocket {\n  _id: string;\n  _authenticated: boolean;\n}\n\ninterface IRPCResult {\n  [x: string]: string;\n}\n\nexport class Server extends EventEmitter\n{\n    private namespaces: INamespace\n    private dataPack: DataPack<any, string>\n    wss: InstanceType<typeof WebSocketServer>\n\n    /**\n   * Instantiate a Server class.\n   * @constructor\n   * @param {Object} options - ws constructor's parameters with rpc\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {Server} - returns a new Server instance\n   */\n    constructor(\n        options: NodeWebSocket.ServerOptions,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        /**\n     * Stores all connected sockets with a universally unique identifier\n     * in the appropriate namespace.\n     * Stores all rpc methods to specific namespaces. \"/\" by default.\n     * Stores all events as keys and subscribed users in array as value\n     * @private\n     * @name namespaces\n     * @param {Object} namespaces.rpc_methods\n     * @param {Map} namespaces.clients\n     * @param {Object} namespaces.events\n     */\n        this.namespaces = {}\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        this.wss = new WebSocketServer(options)\n\n        this.wss.on(\"listening\", () => this.emit(\"listening\"))\n\n        this.wss.on(\"connection\", (socket: IClientWebSocket, request) =>\n        {\n            const u = url.parse(request.url, true)\n            const ns = u.pathname\n\n            if (u.query.socket_id) socket._id = u.query.socket_id as string\n            else socket._id = uuidv1()\n\n            // unauthenticated by default\n            socket[\"_authenticated\"] = false\n\n            // propagate socket errors\n            socket.on(\"error\", (error) => this.emit(\"socket-error\", socket, error))\n\n            // cleanup after the socket gets disconnected\n            socket.on(\"close\", () =>\n            {\n                this.namespaces[ns].clients.delete(socket._id)\n\n                for (const event of Object.keys(this.namespaces[ns].events))\n                {\n                    const index = this.namespaces[ns].events[event].sockets.indexOf(\n                        socket._id\n                    )\n\n                    if (index >= 0)\n                        this.namespaces[ns].events[event].sockets.splice(index, 1)\n                }\n\n                this.emit(\"disconnection\", socket)\n            })\n\n            if (!this.namespaces[ns]) this._generateNamespace(ns)\n\n            // store socket and method\n            this.namespaces[ns].clients.set(socket._id, socket)\n\n            this.emit(\"connection\", socket, request)\n\n            return this._handleRPC(socket, ns)\n        })\n\n        this.wss.on(\"error\", (error) => this.emit(\"error\", error))\n    }\n\n    /**\n   * Registers an RPC method.\n   * @method\n   * @param {String} name - method name\n   * @param {Function} fn - a callee function\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Object} - returns an IMethod object\n   */\n    register(\n        name: string,\n        fn: (params: IRPCMethodParams, socket_id: string) => void,\n        ns = \"/\"\n    )\n    {\n        if (!this.namespaces[ns]) this._generateNamespace(ns)\n\n        this.namespaces[ns].rpc_methods[name] = {\n            fn: fn,\n            protected: false,\n        }\n\n        return {\n            protected: () => this._makeProtectedMethod(name, ns),\n            public: () => this._makePublicMethod(name, ns),\n        } as IMethod\n    }\n\n    /**\n   * Sets an auth method.\n   * @method\n   * @param {Function} fn - an arbitrary auth method\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Undefined}\n   */\n    setAuth(\n        fn: (params: IRPCMethodParams, socket_id: string) => Promise<boolean>,\n        ns = \"/\"\n    )\n    {\n        this.register(\"rpc.login\", fn, ns)\n    }\n\n    /**\n   * Marks an RPC method as protected.\n   * @method\n   * @param {String} name - method name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makeProtectedMethod(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].rpc_methods[name].protected = true\n    }\n\n    /**\n   * Marks an RPC method as public.\n   * @method\n   * @param {String} name - method name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makePublicMethod(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].rpc_methods[name].protected = false\n    }\n\n    /**\n   * Marks an event as protected.\n   * @method\n   * @param {String} name - event name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makeProtectedEvent(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].events[name].protected = true\n    }\n\n    /**\n   * Marks an event as public.\n   * @method\n   * @param {String} name - event name\n   * @param {String} ns - namespace identifier\n   * @return {Undefined}\n   */\n    private _makePublicEvent(name: string, ns = \"/\")\n    {\n        this.namespaces[ns].events[name].protected = false\n    }\n\n    /**\n   * Removes a namespace and closes all connections\n   * @method\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Undefined}\n   */\n    closeNamespace(ns: string)\n    {\n        const namespace = this.namespaces[ns]\n\n        if (namespace)\n        {\n            delete namespace.rpc_methods\n            delete namespace.events\n\n            for (const socket of namespace.clients.values()) socket.close()\n\n            delete this.namespaces[ns]\n        }\n    }\n\n    /**\n   * Creates a new event that can be emitted to clients.\n   * @method\n   * @param {String} name - event name\n   * @param {String} ns - namespace identifier\n   * @throws {TypeError}\n   * @return {Object} - returns an IEvent object\n   */\n    event(name: string, ns = \"/\"): IEvent\n    {\n        if (!this.namespaces[ns]) this._generateNamespace(ns)\n        else\n        {\n            const index = this.namespaces[ns].events[name]\n\n            if (index !== undefined)\n                throw new Error(`Already registered event ${ns}${name}`)\n        }\n\n        this.namespaces[ns].events[name] = {\n            sockets: [],\n            protected: false,\n        }\n\n        // forward emitted event to subscribers\n        this.on(name, (...params) =>\n        {\n            // flatten an object if no spreading is wanted\n            if (params.length === 1 && params[0] instanceof Object)\n                params = params[0]\n\n            for (const socket_id of this.namespaces[ns].events[name].sockets)\n            {\n                const socket = this.namespaces[ns].clients.get(socket_id)\n\n                if (!socket) continue\n\n                socket.send(\n                    this.dataPack.encode({\n                        notification: name,\n                        params,\n                    })\n                )\n            }\n        })\n\n        return {\n            protected: () => this._makeProtectedEvent(name, ns),\n            public: () => this._makePublicEvent(name, ns),\n        }\n    }\n\n    /**\n   * Returns a requested namespace object\n   * @method\n   * @param {String} name - namespace identifier\n   * @throws {TypeError}\n   * @return {Object} - namespace object\n   */\n    of(name: string)\n    {\n        if (!this.namespaces[name]) this._generateNamespace(name)\n\n        const self = this\n\n        return {\n            // self.register convenience method\n            register(\n                fn_name: string,\n                fn: (params: IRPCMethodParams) => void\n            ): IMethod\n            {\n                if (arguments.length !== 2)\n                    throw new Error(\"must provide exactly two arguments\")\n\n                if (typeof fn_name !== \"string\")\n                    throw new Error(\"name must be a string\")\n\n                if (typeof fn !== \"function\")\n                    throw new Error(\"handler must be a function\")\n\n                return self.register(fn_name, fn, name)\n            },\n\n            // self.event convenience method\n            event(ev_name: string): IEvent\n            {\n                if (arguments.length !== 1)\n                    throw new Error(\"must provide exactly one argument\")\n\n                if (typeof ev_name !== \"string\")\n                    throw new Error(\"name must be a string\")\n\n                return self.event(ev_name, name)\n            },\n\n            // self.eventList convenience method\n            get eventList()\n            {\n                return Object.keys(self.namespaces[name].events)\n            },\n\n            /**\n       * Emits a specified event to this namespace.\n       * @inner\n       * @method\n       * @param {String} event - event name\n       * @param {Array} params - event parameters\n       * @return {Undefined}\n       */\n            emit(event: string, ...params: Array<string>)\n            {\n                const nsEvent = self.namespaces[name].events[event]\n\n                if (nsEvent)\n                    for (const socket_id of nsEvent.sockets)\n                    {\n                        const socket = self.namespaces[name].clients.get(socket_id)\n\n                        if (!socket) continue\n\n                        socket.send(\n                            self.dataPack.encode({\n                                notification: event,\n                                params,\n                            })\n                        )\n                    }\n            },\n\n            /**\n       * Returns a name of this namespace.\n       * @inner\n       * @method\n       * @kind constant\n       * @return {String}\n       */\n            get name()\n            {\n                return name\n            },\n\n            /**\n       * Returns a hash of websocket objects connected to this namespace.\n       * @inner\n       * @method\n       * @return {Object}\n       */\n            connected()\n            {\n                const socket_ids = [...self.namespaces[name].clients.keys()]\n\n                return socket_ids.reduce(\n                    (acc, curr) => ({\n                        ...acc,\n                        [curr]: self.namespaces[name].clients.get(curr),\n                    }),\n                    {}\n                )\n            },\n\n            /**\n       * Returns a list of client unique identifiers connected to this namespace.\n       * @inner\n       * @method\n       * @return {Array}\n       */\n            clients()\n            {\n                return self.namespaces[name]\n            },\n        }\n    }\n\n    /**\n   * Lists all created events in a given namespace. Defaults to \"/\".\n   * @method\n   * @param {String} ns - namespaces identifier\n   * @readonly\n   * @return {Array} - returns a list of created events\n   */\n    eventList(ns = \"/\")\n    {\n        if (!this.namespaces[ns]) return []\n\n        return Object.keys(this.namespaces[ns].events)\n    }\n\n    /**\n   * Creates a JSON-RPC 2.0 compliant error\n   * @method\n   * @param {Number} code - indicates the error type that occurred\n   * @param {String} message - provides a short description of the error\n   * @param {String|Object} data - details containing additional information about the error\n   * @return {Object}\n   */\n    createError(code: number, message: string, data: string | object)\n    {\n        return {\n            code: code,\n            message: message,\n            data: data || null,\n        }\n    }\n\n    /**\n   * Closes the server and terminates all clients.\n   * @method\n   * @return {Promise}\n   */\n    close()\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            try\n            {\n                this.wss.close()\n                this.emit(\"close\")\n                resolve()\n            }\n            catch (error)\n            {\n                reject(error)\n            }\n        })\n    }\n\n    /**\n   * Handles all WebSocket JSON RPC 2.0 requests.\n   * @private\n   * @param {Object} socket - ws socket instance\n   * @param {String} ns - namespaces identifier\n   * @return {Undefined}\n   */\n    private _handleRPC(socket: IClientWebSocket, ns = \"/\")\n    {\n        socket.on(\"message\", async (data: any) =>\n        {\n            const msg_options: Parameters<NodeWebSocket[\"send\"]>[1] = {}\n\n            if (data instanceof ArrayBuffer)\n            {\n                msg_options.binary = true\n\n                data = Buffer.from(data).toString()\n            }\n\n            if (socket.readyState !== 1) return // TODO: should have debug logs here\n\n            let parsedData: any\n\n            try\n            {\n                parsedData = this.dataPack.decode(data as string)\n            }\n            catch (error)\n            {\n                return socket.send(\n                    this.dataPack.encode({\n                        jsonrpc: \"2.0\",\n                        error: createError(-32700, error.toString()),\n                        id: null,\n                    }),\n                    msg_options\n                )\n            }\n\n            if (Array.isArray(parsedData))\n            {\n                if (!parsedData.length)\n                    return socket.send(\n                        this.dataPack.encode({\n                            jsonrpc: \"2.0\",\n                            error: createError(-32600, \"Invalid array\"),\n                            id: null,\n                        }),\n                        msg_options\n                    )\n\n                const responses = []\n\n                for (const message of parsedData)\n                {\n                    const response = await this._runMethod(message, socket._id, ns)\n\n                    if (!response) continue\n\n                    responses.push(response)\n                }\n\n                if (!responses.length) return\n\n                return socket.send(this.dataPack.encode(responses), msg_options)\n            }\n\n            const response = await this._runMethod(parsedData, socket._id, ns)\n\n            if (!response) return\n\n            return socket.send(this.dataPack.encode(response), msg_options)\n        })\n    }\n\n    /**\n   * Runs a defined RPC method.\n   * @private\n   * @param {Object} message - a message received\n   * @param {Object} socket_id - user's socket id\n   * @param {String} ns - namespaces identifier\n   * @return {Object|undefined}\n   */\n    private async _runMethod(message: any, socket_id: string, ns = \"/\")\n    {\n        if (typeof message !== \"object\" || message === null)\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600),\n                id: null,\n            }\n\n        if (message.jsonrpc !== \"2.0\")\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600, \"Invalid JSON RPC version\"),\n                id: message.id || null,\n            }\n\n        if (!message.method)\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32602, \"Method not specified\"),\n                id: message.id || null,\n            }\n\n        if (typeof message.method !== \"string\")\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600, \"Invalid method name\"),\n                id: message.id || null,\n            }\n\n        if (message.params && typeof message.params === \"string\")\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32600),\n                id: message.id || null,\n            }\n\n        if (message.method === \"rpc.on\")\n        {\n            if (!message.params)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: createError(-32000),\n                    id: message.id || null,\n                }\n\n            const results: IRPCMethodParams = {}\n\n            const event_names = Object.keys(this.namespaces[ns].events)\n\n            for (const name of message.params)\n            {\n                const index = event_names.indexOf(name)\n                const namespace = this.namespaces[ns]\n\n                if (index === -1)\n                {\n                    results[name] = \"provided event invalid\"\n                    continue\n                }\n\n                // reject request if event is protected and if client is not authenticated\n                if (\n                    namespace.events[event_names[index]].protected === true &&\n          namespace.clients.get(socket_id)[\"_authenticated\"] === false\n                )\n                {\n                    return {\n                        jsonrpc: \"2.0\",\n                        error: createError(-32606),\n                        id: message.id || null,\n                    }\n                }\n\n                const socket_index =\n          namespace.events[event_names[index]].sockets.indexOf(socket_id)\n                if (socket_index >= 0)\n                {\n                    results[name] = \"socket has already been subscribed to event\"\n                    continue\n                }\n                namespace.events[event_names[index]].sockets.push(socket_id)\n\n                results[name] = \"ok\"\n            }\n\n            return {\n                jsonrpc: \"2.0\",\n                result: results,\n                id: message.id || null,\n            }\n        }\n        else if (message.method === \"rpc.off\")\n        {\n            if (!message.params)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: createError(-32000),\n                    id: message.id || null,\n                }\n\n            const results: IRPCResult = {}\n\n            for (const name of message.params)\n            {\n                if (!this.namespaces[ns].events[name])\n                {\n                    results[name] = \"provided event invalid\"\n                    continue\n                }\n\n                const index =\n          this.namespaces[ns].events[name].sockets.indexOf(socket_id)\n\n                if (index === -1)\n                {\n                    results[name] = \"not subscribed\"\n                    continue\n                }\n\n                this.namespaces[ns].events[name].sockets.splice(index, 1)\n                results[name] = \"ok\"\n            }\n\n            return {\n                jsonrpc: \"2.0\",\n                result: results,\n                id: message.id || null,\n            }\n        }\n        else if (message.method === \"rpc.login\")\n        {\n            if (!message.params)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: createError(-32604),\n                    id: message.id || null,\n                }\n        }\n\n        if (!this.namespaces[ns].rpc_methods[message.method])\n        {\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32601),\n                id: message.id || null,\n            }\n        }\n\n        let response = null\n\n        // reject request if method is protected and if client is not authenticated\n        if (\n            this.namespaces[ns].rpc_methods[message.method].protected === true &&\n      this.namespaces[ns].clients.get(socket_id)[\"_authenticated\"] === false\n        )\n        {\n            return {\n                jsonrpc: \"2.0\",\n                error: createError(-32605),\n                id: message.id || null,\n            }\n        }\n\n        try\n        {\n            response = await this.namespaces[ns].rpc_methods[message.method].fn(\n                message.params,\n                socket_id\n            )\n        }\n        catch (error)\n        {\n            if (!message.id) return\n\n            if (error instanceof Error)\n                return {\n                    jsonrpc: \"2.0\",\n                    error: {\n                        code: -32000,\n                        message: error.name,\n                        data: error.message,\n                    },\n                    id: message.id,\n                }\n\n            return {\n                jsonrpc: \"2.0\",\n                error: error,\n                id: message.id,\n            }\n        }\n\n        // client sent a notification, so we won't need a reply\n        if (!message.id) return\n\n        // if login middleware returned true, set connection as authenticated\n        if (message.method === \"rpc.login\" && response === true)\n        {\n            const s = this.namespaces[ns].clients.get(socket_id)\n            s[\"_authenticated\"] = true\n            this.namespaces[ns].clients.set(socket_id, s)\n        }\n\n        return {\n            jsonrpc: \"2.0\",\n            result: response,\n            id: message.id,\n        }\n    }\n\n    /**\n   * Generate a new namespace store.\n   * Also preregister some special namespace methods.\n   * @private\n   * @param {String} name - namespaces identifier\n   * @return {undefined}\n   */\n    private _generateNamespace(name: string)\n    {\n        this.namespaces[name] = {\n            rpc_methods: {\n                __listMethods: {\n                    fn: () => Object.keys(this.namespaces[name].rpc_methods),\n                    protected: false,\n                },\n            },\n            clients: new Map(),\n            events: {},\n        }\n    }\n}\n\nconst RPC_ERRORS = new Map([\n    [-32000, \"Event not provided\"],\n    [-32600, \"Invalid Request\"],\n    [-32601, \"Method not found\"],\n    [-32602, \"Invalid params\"],\n    [-32603, \"Internal error\"],\n    [-32604, \"Params not found\"],\n    [-32605, \"Method forbidden\"],\n    [-32606, \"Event forbidden\"],\n    [-32700, \"Parse error\"],\n])\n\n/**\n * Creates a JSON-RPC 2.0-compliant error.\n * @param {Number} code - error code\n * @param {String} details - error details\n * @return {Object}\n */\nexport function createError(code: number, details?: string)\n{\n    const error: IRPCError = {\n        code: code,\n        message: RPC_ERRORS.get(code) || \"Internal Server Error\",\n    }\n\n    if (details) error[\"data\"] = details\n\n    return error\n}\n", "\"use strict\"\n\nimport { WebSocket } from \"./lib/client/websocket.js\"\nimport { CommonClient } from \"./lib/client.js\"\nimport {\n    NodeWebSocketTypeOptions,\n    IWSClientAdditionalOptions,\n    ICommonWebSocketFactory,\n} from \"./lib/client/client.types.js\"\n\nexport class Client extends CommonClient\n{\n    constructor(\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        }: IWSClientAdditionalOptions & NodeWebSocketTypeOptions = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string\n    )\n    {\n        super(\n      WebSocket as ICommonWebSocketFactory,\n      address,\n      {\n          autoconnect,\n          reconnect,\n          reconnect_interval,\n          max_reconnects,\n          ...rest_options,\n      },\n      generate_request_id\n        )\n    }\n}\n\nexport * from \"./lib/client.js\"\nexport * from \"./lib/client/websocket.js\"\nexport * from \"./lib/client/client.types.js\"\nexport * from \"./lib/server.js\"\nexport * from \"./lib/utils.js\"\n"]}