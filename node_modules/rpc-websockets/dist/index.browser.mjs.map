{"version": 3, "sources": ["../src/lib/client/websocket.browser.ts", "../src/lib/utils.ts", "../src/lib/client.ts", "../src/index.browser.ts"], "names": ["EventEmitter"], "mappings": ";;;;AAeA,IAAM,oBAAA,GAAN,cAAmC,YACnC,CAAA;AAAA,EACI,MAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAA,CAAY,OAAiB,EAAA,OAAA,EAAa,SAC1C,EAAA;AACI,IAAM,KAAA,EAAA;AAEN,IAAA,IAAA,CAAK,MAAS,GAAA,IAAI,MAAO,CAAA,SAAA,CAAU,SAAS,SAAS,CAAA;AAErD,IAAA,IAAA,CAAK,MAAO,CAAA,MAAA,GAAS,MAAM,IAAA,CAAK,KAAK,MAAM,CAAA;AAC3C,IAAK,IAAA,CAAA,MAAA,CAAO,YAAY,CAAC,KAAA,KAAU,KAAK,IAAK,CAAA,SAAA,EAAW,MAAM,IAAI,CAAA;AAClE,IAAA,IAAA,CAAK,OAAO,OAAU,GAAA,CAAC,UAAU,IAAK,CAAA,IAAA,CAAK,SAAS,KAAK,CAAA;AACzD,IAAK,IAAA,CAAA,MAAA,CAAO,OAAU,GAAA,CAAC,KACvB,KAAA;AACI,MAAA,IAAA,CAAK,IAAK,CAAA,OAAA,EAAS,KAAM,CAAA,IAAA,EAAM,MAAM,MAAM,CAAA;AAAA,KAC/C;AAAA;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAA,CACI,IACA,EAAA,iBAAA,EAGA,QAEJ,EAAA;AACI,IAAA,MAAM,KAAK,QAAY,IAAA,iBAAA;AAEvB,IACA,IAAA;AACI,MAAK,IAAA,CAAA,MAAA,CAAO,KAAK,IAAI,CAAA;AACrB,MAAG,EAAA,EAAA;AAAA,aAEA,KACP,EAAA;AACI,MAAA,EAAA,CAAG,KAAK,CAAA;AAAA;AACZ;AACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,KAAA,CAAM,MAAe,MACrB,EAAA;AACI,IAAK,IAAA,CAAA,MAAA,CAAO,KAAM,CAAA,IAAA,EAAM,MAAM,CAAA;AAAA;AAClC,EAEA,gBAAA,CACI,IACA,EAAA,QAAA,EACA,OAEJ,EAAA;AACI,IAAA,IAAA,CAAK,MAAO,CAAA,gBAAA,CAAiB,IAAM,EAAA,QAAA,EAAU,OAAO,CAAA;AAAA;AAE5D,CAAA;AASO,SAAS,SAAA,CACZ,SACA,OAEJ,EAAA;AACI,EAAO,OAAA,IAAI,oBAAqB,CAAA,OAAA,EAAS,OAAO,CAAA;AACpD;;;AChGO,IAAM,kBAAN,MACP;AAAA,EACI,OAAO,KACP,EAAA;AACI,IAAO,OAAA,IAAA,CAAK,UAAU,KAAK,CAAA;AAAA;AAC/B,EAEA,OAAO,KACP,EAAA;AACI,IAAO,OAAA,IAAA,CAAK,MAAM,KAAK,CAAA;AAAA;AAE/B;;;ACea,IAAA,YAAA,GAAN,cAA2BA,YAClC,CAAA;AAAA,EACY,OAAA;AAAA,EACA,MAAA;AAAA,EACA,KAAA;AAAA,EACA,OAAA;AAAA,EACA,WAAA;AAAA,EACA,KAAA;AAAA,EACA,SAAA;AAAA,EACA,kBAAA;AAAA,EACA,kBAAA;AAAA,EACA,cAAA;AAAA,EACA,YAAA;AAAA,EAEA,kBAAA;AAAA,EACA,mBAAA;AAAA,EAIA,MAAA;AAAA,EACA,gBAAA;AAAA,EACA,QAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYR,WAAA,CACI,gBACA,EAAA,OAAA,GAAU,qBACV,EAAA;AAAA,IACI,WAAc,GAAA,IAAA;AAAA,IACd,SAAY,GAAA,IAAA;AAAA,IACZ,kBAAqB,GAAA,GAAA;AAAA,IACrB,cAAiB,GAAA,CAAA;AAAA,IACjB,GAAG;AAAA,GACH,GAAA,EACJ,EAAA,mBAAA,EAIA,QAEJ,EAAA;AACI,IAAM,KAAA,EAAA;AAEN,IAAA,IAAA,CAAK,gBAAmB,GAAA,gBAAA;AAExB,IAAA,IAAA,CAAK,QAAQ,EAAC;AACd,IAAA,IAAA,CAAK,MAAS,GAAA,CAAA;AAEd,IAAA,IAAA,CAAK,OAAU,GAAA,OAAA;AACf,IAAA,IAAA,CAAK,WAAc,GAAA,WAAA;AACnB,IAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;AACb,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AACjB,IAAA,IAAA,CAAK,kBAAqB,GAAA,MAAA;AAC1B,IAAA,IAAA,CAAK,kBAAqB,GAAA,kBAAA;AAC1B,IAAA,IAAA,CAAK,cAAiB,GAAA,cAAA;AACtB,IAAA,IAAA,CAAK,YAAe,GAAA,YAAA;AACpB,IAAA,IAAA,CAAK,kBAAqB,GAAA,CAAA;AAC1B,IAAA,IAAA,CAAK,mBAAsB,GAAA,mBAAA,KAAwB,MAAM,OAAO,IAAK,CAAA,MAAA,KAAW,QAC1E,GAAA,EAAE,IAAK,CAAA,MAAA,GACP,MAAO,CAAA,IAAA,CAAK,MAAM,CAAI,GAAA,CAAA,CAAA;AAE5B,IAAA,IAAI,CAAC,QAAA,EAAe,IAAA,CAAA,QAAA,GAAW,IAAI,eAAgB,EAAA;AAAA,cACzC,QAAW,GAAA,QAAA;AAErB,IAAA,IAAI,IAAK,CAAA,WAAA;AACL,MAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAS,EAAA;AAAA,QACxB,aAAa,IAAK,CAAA,WAAA;AAAA,QAClB,WAAW,IAAK,CAAA,SAAA;AAAA,QAChB,oBAAoB,IAAK,CAAA,kBAAA;AAAA,QACzB,gBAAgB,IAAK,CAAA,cAAA;AAAA,QACrB,GAAG,IAAK,CAAA;AAAA,OACX,CAAA;AAAA;AACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OACA,GAAA;AACI,IAAA,IAAI,KAAK,MAAQ,EAAA;AAEjB,IAAK,IAAA,CAAA,QAAA,CAAS,KAAK,OAAS,EAAA;AAAA,MACxB,aAAa,IAAK,CAAA,WAAA;AAAA,MAClB,WAAW,IAAK,CAAA,SAAA;AAAA,MAChB,oBAAoB,IAAK,CAAA,kBAAA;AAAA,MACzB,gBAAgB,IAAK,CAAA,cAAA;AAAA,MACrB,GAAG,IAAK,CAAA;AAAA,KACX,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,IACI,CAAA,MAAA,EACA,MACA,EAAA,OAAA,EACA,OAEJ,EAAA;AACI,IAAA,IAAI,CAAC,OAAA,IAAW,QAAa,KAAA,OAAO,OACpC,EAAA;AACI,MAAU,OAAA,GAAA,OAAA;AACV,MAAU,OAAA,GAAA,IAAA;AAAA;AAGd,IAAA,OAAO,IAAI,OAAA,CAAQ,CAAC,OAAA,EAAS,MAC7B,KAAA;AACI,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA,OAAO,OAAO,IAAI,KAAA,CAAM,kBAAkB,CAAC,CAAA;AAE5D,MAAA,MAAM,MAAS,GAAA,IAAA,CAAK,mBAAoB,CAAA,MAAA,EAAQ,MAAM,CAAA;AAEtD,MAAA,MAAM,OAAU,GAAA;AAAA,QACZ,OAAS,EAAA,KAAA;AAAA,QACT,MAAA;AAAA,QACA,QAAQ,MAAU,IAAA,MAAA;AAAA,QAClB,EAAI,EAAA;AAAA,OACR;AAEA,MAAK,IAAA,CAAA,MAAA,CAAO,KAAK,IAAK,CAAA,QAAA,CAAS,OAAO,OAAO,CAAA,EAAG,OAAS,EAAA,CAAC,KAC1D,KAAA;AACI,QAAI,IAAA,KAAA,EAAc,OAAA,MAAA,CAAO,KAAK,CAAA;AAE9B,QAAK,IAAA,CAAA,KAAA,CAAM,MAAM,CAAI,GAAA,EAAE,SAAS,CAAC,OAAA,EAAS,MAAM,CAAE,EAAA;AAElD,QAAA,IAAI,OACJ,EAAA;AACI,UAAA,IAAA,CAAK,KAAM,CAAA,MAAM,CAAE,CAAA,OAAA,GAAU,WAAW,MACxC;AACI,YAAO,OAAA,IAAA,CAAK,MAAM,MAAM,CAAA;AACxB,YAAO,MAAA,CAAA,IAAI,KAAM,CAAA,eAAe,CAAC,CAAA;AAAA,aAClC,OAAO,CAAA;AAAA;AACd,OACH,CAAA;AAAA,KACJ,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,MAAM,MACZ,EAAA;AACI,IAAA,MAAM,IAAO,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,aAAa,MAAM,CAAA;AAEhD,IAAA,IAAI,CAAC,IAAA,EAAY,MAAA,IAAI,MAAM,uBAAuB,CAAA;AAElD,IAAO,OAAA,IAAA;AAAA;AACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,WACN,GAAA;AACI,IAAO,OAAA,MAAM,IAAK,CAAA,IAAA,CAAK,eAAe,CAAA;AAAA;AAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAA,CAAO,QAAgB,MACvB,EAAA;AACI,IAAA,OAAO,IAAI,OAAA,CAAc,CAAC,OAAA,EAAS,MACnC,KAAA;AACI,MAAI,IAAA,CAAC,KAAK,KAAO,EAAA,OAAO,OAAO,IAAI,KAAA,CAAM,kBAAkB,CAAC,CAAA;AAE5D,MAAA,MAAM,OAAU,GAAA;AAAA,QACZ,OAAS,EAAA,KAAA;AAAA,QACT,MAAA;AAAA,QACA;AAAA,OACJ;AAEA,MAAK,IAAA,CAAA,MAAA,CAAO,KAAK,IAAK,CAAA,QAAA,CAAS,OAAO,OAAO,CAAA,EAAG,CAAC,KACjD,KAAA;AACI,QAAI,IAAA,KAAA,EAAc,OAAA,MAAA,CAAO,KAAK,CAAA;AAE9B,QAAQ,OAAA,EAAA;AAAA,OACX,CAAA;AAAA,KACJ,CAAA;AAAA;AACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,UAAU,KAChB,EAAA;AACI,IAAA,IAAI,OAAO,KAAA,KAAU,QAAU,EAAA,KAAA,GAAQ,CAAC,KAAK,CAAA;AAE7C,IAAA,MAAM,MAAS,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,UAAU,KAAK,CAAA;AAE9C,IAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,MAAA,CAAO,KAAK,CAAM,KAAA,IAAA;AAC/C,MAAA,MAAM,IAAI,KAAA;AAAA,QACN,kCAAqC,GAAA,KAAA,GAAQ,UAAa,GAAA,MAAA,CAAO,KAAK;AAAA,OAC1E;AAEJ,IAAO,OAAA,MAAA;AAAA;AACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,YAAY,KAClB,EAAA;AACI,IAAA,IAAI,OAAO,KAAA,KAAU,QAAU,EAAA,KAAA,GAAQ,CAAC,KAAK,CAAA;AAE7C,IAAA,MAAM,MAAS,GAAA,MAAM,IAAK,CAAA,IAAA,CAAK,WAAW,KAAK,CAAA;AAE/C,IAAA,IAAI,OAAO,KAAA,KAAU,QAAY,IAAA,MAAA,CAAO,KAAK,CAAM,KAAA,IAAA;AAC/C,MAAM,MAAA,IAAI,KAAM,CAAA,2CAAA,GAA8C,MAAM,CAAA;AAExE,IAAO,OAAA,MAAA;AAAA;AACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,KAAA,CAAM,MAAe,IACrB,EAAA;AACI,IAAA,IAAA,CAAK,MAAO,CAAA,KAAA,CAAM,IAAQ,IAAA,GAAA,EAAM,IAAI,CAAA;AAAA;AACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,SACjB,EAAA;AACI,IAAA,IAAA,CAAK,SAAY,GAAA,SAAA;AAAA;AACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,qBAAqB,QACrB,EAAA;AACI,IAAA,IAAA,CAAK,kBAAqB,GAAA,QAAA;AAAA;AAC9B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,cACjB,EAAA;AACI,IAAA,IAAA,CAAK,cAAiB,GAAA,cAAA;AAAA;AAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUQ,QAAA,CACJ,SACA,OAEJ,EAAA;AACI,IAAA,YAAA,CAAa,KAAK,kBAAkB,CAAA;AACpC,IAAA,IAAA,CAAK,MAAS,GAAA,IAAA,CAAK,gBAAiB,CAAA,OAAA,EAAS,OAAO,CAAA;AAEpD,IAAK,IAAA,CAAA,MAAA,CAAO,gBAAiB,CAAA,MAAA,EAAQ,MACrC;AACI,MAAA,IAAA,CAAK,KAAQ,GAAA,IAAA;AACb,MAAA,IAAA,CAAK,KAAK,MAAM,CAAA;AAChB,MAAA,IAAA,CAAK,kBAAqB,GAAA,CAAA;AAAA,KAC7B,CAAA;AAED,IAAA,IAAA,CAAK,OAAO,gBAAiB,CAAA,SAAA,EAAW,CAAC,EAAE,IAAA,EAAM,SACjD,KAAA;AACI,MAAA,IAAI,OAAmB,YAAA,WAAA;AACnB,QAAA,OAAA,GAAU,MAAO,CAAA,IAAA,CAAK,OAAO,CAAA,CAAE,QAAS,EAAA;AAE5C,MACA,IAAA;AACI,QAAU,OAAA,GAAA,IAAA,CAAK,QAAS,CAAA,MAAA,CAAO,OAAO,CAAA;AAAA,eAEnC,KACP,EAAA;AACI,QAAA;AAAA;AAIJ,MAAA,IAAI,QAAQ,YAAgB,IAAA,IAAA,CAAK,UAAU,OAAQ,CAAA,YAAY,EAAE,MACjE,EAAA;AACI,QAAA,IAAI,CAAC,MAAA,CAAO,IAAK,CAAA,OAAA,CAAQ,MAAM,CAAE,CAAA,MAAA;AAC7B,UAAO,OAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,YAAY,CAAA;AAEzC,QAAM,MAAA,IAAA,GAAO,CAAC,OAAA,CAAQ,YAAY,CAAA;AAElC,QAAA,IAAI,QAAQ,MAAO,CAAA,WAAA,KAAgB,QAAa,IAAA,CAAA,IAAA,CAAK,QAAQ,MAAM,CAAA;AAAA;AAG/D,UAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,OAAA,CAAQ,OAAO,MAAQ,EAAA,CAAA,EAAA;AACvC,YAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,MAAO,CAAA,CAAC,CAAC,CAAA;AAInC,QAAA,OAAO,OAAQ,CAAA,OAAA,EAAU,CAAA,IAAA,CAAK,MAC9B;AAEI,UAAK,IAAA,CAAA,IAAA,CAAK,KAAM,CAAA,IAAA,EAAM,IAAI,CAAA;AAAA,SAC7B,CAAA;AAAA;AAGL,MAAA,IAAI,CAAC,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAC1B,EAAA;AAEI,QAAA,IAAI,QAAQ,MACZ,EAAA;AAEI,UAAA,OAAO,OAAQ,CAAA,OAAA,EAAU,CAAA,IAAA,CAAK,MAC9B;AACI,YAAA,IAAA,CAAK,IAAK,CAAA,OAAA,CAAQ,MAAQ,EAAA,OAAA,EAAS,MAAM,CAAA;AAAA,WAC5C,CAAA;AAAA;AAGL,QAAA;AAAA;AAIJ,MAAI,IAAA,OAAA,IAAW,YAAY,QAAY,IAAA,OAAA;AACnC,QAAA,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAA,CAAE,QAAQ,CAAC,CAAA;AAAA,UAC5B,IAAI,KAAA;AAAA,YACA;AAAA;AAEJ,SACJ;AAEJ,MAAA,IAAI,IAAK,CAAA,KAAA,CAAM,OAAQ,CAAA,EAAE,CAAE,CAAA,OAAA;AACvB,QAAA,YAAA,CAAa,IAAK,CAAA,KAAA,CAAM,OAAQ,CAAA,EAAE,EAAE,OAAO,CAAA;AAE/C,MAAI,IAAA,OAAA,CAAQ,KAAO,EAAA,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAA,CAAE,OAAQ,CAAA,CAAC,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAA;AAAA,WAC7D,IAAA,CAAK,MAAM,OAAQ,CAAA,EAAE,EAAE,OAAQ,CAAA,CAAC,CAAE,CAAA,OAAA,CAAQ,MAAM,CAAA;AAErD,MAAO,OAAA,IAAA,CAAK,KAAM,CAAA,OAAA,CAAQ,EAAE,CAAA;AAAA,KAC/B,CAAA;AAED,IAAK,IAAA,CAAA,MAAA,CAAO,iBAAiB,OAAS,EAAA,CAAC,UAAU,IAAK,CAAA,IAAA,CAAK,OAAS,EAAA,KAAK,CAAC,CAAA;AAE1E,IAAA,IAAA,CAAK,OAAO,gBAAiB,CAAA,OAAA,EAAS,CAAC,EAAE,IAAA,EAAM,QAC/C,KAAA;AACI,MAAA,IAAI,IAAK,CAAA,KAAA;AAEL,QAAA,UAAA,CAAW,MAAM,IAAK,CAAA,IAAA,CAAK,SAAS,IAAM,EAAA,MAAM,GAAG,CAAC,CAAA;AAExD,MAAA,IAAA,CAAK,KAAQ,GAAA,KAAA;AACb,MAAA,IAAA,CAAK,MAAS,GAAA,MAAA;AAEd,MAAA,IAAI,SAAS,GAAM,EAAA;AAEnB,MAAK,IAAA,CAAA,kBAAA,EAAA;AAEL,MAAA,IACI,KAAK,SACZ,KAAA,IAAA,CAAK,iBAAiB,IAAK,CAAA,kBAAA,IAC1B,KAAK,cAAmB,KAAA,CAAA,CAAA;AAElB,QAAA,IAAA,CAAK,kBAAqB,GAAA,UAAA;AAAA,UACtB,MAAM,IAAA,CAAK,QAAS,CAAA,OAAA,EAAS,OAAO,CAAA;AAAA,UACpC,IAAK,CAAA;AAAA,SACT;AAAA,KACP,CAAA;AAAA;AAET;;;ACzba,IAAA,MAAA,GAAN,cAAqB,YAC5B,CAAA;AAAA,EACI,WAAA,CACI,UAAU,qBACV,EAAA;AAAA,IACI,WAAc,GAAA,IAAA;AAAA,IACd,SAAY,GAAA,IAAA;AAAA,IACZ,kBAAqB,GAAA,GAAA;AAAA,IACrB,cAAiB,GAAA;AAAA,GACrB,GAAgC,EAAC,EACjC,mBAKJ,EAAA;AACI,IAAA,KAAA;AAAA,MACI,SAAA;AAAA,MACA,OAAA;AAAA,MACA;AAAA,QACI,WAAA;AAAA,QACA,SAAA;AAAA,QACA,kBAAA;AAAA,QACA;AAAA,OACJ;AAAA,MACA;AAAA,KACJ;AAAA;AAER", "file": "index.browser.mjs", "sourcesContent": ["/**\n * WebSocket implements a browser-side WebSocket specification.\n * @module Client\n */\n\n\"use strict\"\n\nimport { EventEmitter } from \"eventemitter3\"\n\nimport {\n    BrowserWebSocketType,\n    NodeWebSocketType,\n    IWSClientAdditionalOptions,\n} from \"./client.types.js\"\n\nclass WebSocket<PERSON>rowserImpl extends EventEmitter\n{\n    socket: BrowserWebSocketType\n\n    /** Instantiate a WebSocket class\n   * @constructor\n   * @param {String} address - url to a websocket server\n   * @param {(Object)} options - websocket options\n   * @param {(String|Array)} protocols - a list of protocols\n   * @return {WebSocketBrowserImpl} - returns a WebSocket instance\n   */\n    constructor(address: string, options: {}, protocols?: string | string[])\n    {\n        super()\n\n        this.socket = new window.WebSocket(address, protocols)\n\n        this.socket.onopen = () => this.emit(\"open\")\n        this.socket.onmessage = (event) => this.emit(\"message\", event.data)\n        this.socket.onerror = (error) => this.emit(\"error\", error)\n        this.socket.onclose = (event) =>\n        {\n            this.emit(\"close\", event.code, event.reason)\n        }\n    }\n\n    /**\n   * Sends data through a websocket connection\n   * @method\n   * @param {(String|Object)} data - data to be sent via websocket\n   * @param {Object} optionsOrCallback - ws options\n   * @param {Function} callback - a callback called once the data is sent\n   * @return {Undefined}\n   */\n    send(\n        data: Parameters<BrowserWebSocketType[\"send\"]>[0],\n        optionsOrCallback: (\n      error?: Error\n    ) => void | Parameters<NodeWebSocketType[\"send\"]>[1],\n        callback?: () => void\n    )\n    {\n        const cb = callback || optionsOrCallback\n\n        try\n        {\n            this.socket.send(data)\n            cb()\n        }\n        catch (error)\n        {\n            cb(error)\n        }\n    }\n\n    /**\n   * Closes an underlying socket\n   * @method\n   * @param {Number} code - status code explaining why the connection is being closed\n   * @param {String} reason - a description why the connection is closing\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    close(code?: number, reason?: string)\n    {\n        this.socket.close(code, reason)\n    }\n\n    addEventListener<K extends keyof WebSocketEventMap>(\n        type: K,\n        listener: (ev: WebSocketEventMap[K]) => any,\n        options?: boolean | AddEventListenerOptions\n    ): void\n    {\n        this.socket.addEventListener(type, listener, options)\n    }\n}\n\n/**\n * factory method for common WebSocket instance\n * @method\n * @param {String} address - url to a websocket server\n * @param {(Object)} options - websocket options\n * @return {Undefined}\n */\nexport function WebSocket(\n    address: string,\n    options: IWSClientAdditionalOptions\n)\n{\n    return new WebSocketBrowserImpl(address, options)\n}\n", "\"use strict\"\n\nexport interface DataPack<\n  T,\n  R extends string | ArrayBufferLike | Blob | ArrayBufferView\n> {\n  encode(value: T): R;\n  decode(value: R): T;\n}\n\nexport class DefaultDataPack implements DataPack<Object, string>\n{\n    encode(value: Object): string\n    {\n        return JSON.stringify(value)\n    }\n\n    decode(value: string): Object\n    {\n        return JSON.parse(value)\n    }\n}\n", "/**\n * \"Client\" wraps \"ws\" or a browser-implemented \"WebSocket\" library\n * according to the environment providing JSON RPC 2.0 support on top.\n * @module Client\n */\n\n\"use strict\"\n\nimport NodeWebSocket from \"ws\"\nimport { EventEmitter } from \"eventemitter3\"\nimport {\n    ICommonWebSocket,\n    IWSClientAdditionalOptions,\n    NodeWebSocketType,\n    ICommonWebSocketFactory,\n} from \"./client/client.types.js\"\n\nimport { <PERSON>Pack, DefaultDataPack } from \"./utils.js\"\n\ninterface IQueueElement {\n  promise: [\n    Parameters<ConstructorParameters<typeof Promise>[0]>[0],\n    Parameters<ConstructorParameters<typeof Promise>[0]>[1]\n  ];\n  timeout?: ReturnType<typeof setTimeout>;\n}\n\nexport interface IQueue {\n  [x: number | string]: IQueueElement;\n}\n\nexport interface IWSRequestParams {\n  [x: string]: any;\n  [x: number]: any;\n}\n\nexport class CommonClient extends EventEmitter\n{\n    private address: string\n    private rpc_id: number | string\n    private queue: IQueue\n    private options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    private autoconnect: boolean\n    private ready: boolean\n    private reconnect: boolean\n    private reconnect_timer_id: NodeJS.Timeout\n    private reconnect_interval: number\n    private max_reconnects: number\n    private rest_options: IWSClientAdditionalOptions &\n    NodeWebSocket.ClientOptions\n    private current_reconnects: number\n    private generate_request_id: (\n    method: string,\n    params: object | Array<any>\n  ) => number | string\n    private socket: ICommonWebSocket\n    private webSocketFactory: ICommonWebSocketFactory\n    private dataPack: DataPack<object, string>\n\n    /**\n   * Instantiate a Client class.\n   * @constructor\n   * @param {webSocketFactory} webSocketFactory - factory method for WebSocket\n   * @param {String} address - url to a websocket server\n   * @param {Object} options - ws options object with reconnect parameters\n   * @param {Function} generate_request_id - custom generation request Id\n   * @param {DataPack} dataPack - data pack contains encoder and decoder\n   * @return {CommonClient}\n   */\n    constructor(\n        webSocketFactory: ICommonWebSocketFactory,\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n            ...rest_options\n        } = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string,\n        dataPack?: DataPack<object, string>\n    )\n    {\n        super()\n\n        this.webSocketFactory = webSocketFactory\n\n        this.queue = {}\n        this.rpc_id = 0\n\n        this.address = address\n        this.autoconnect = autoconnect\n        this.ready = false\n        this.reconnect = reconnect\n        this.reconnect_timer_id = undefined\n        this.reconnect_interval = reconnect_interval\n        this.max_reconnects = max_reconnects\n        this.rest_options = rest_options\n        this.current_reconnects = 0\n        this.generate_request_id = generate_request_id || (() => typeof this.rpc_id === \"number\"\n            ? ++this.rpc_id\n            : Number(this.rpc_id) + 1)\n\n        if (!dataPack) this.dataPack = new DefaultDataPack()\n        else this.dataPack = dataPack\n\n        if (this.autoconnect)\n            this._connect(this.address, {\n                autoconnect: this.autoconnect,\n                reconnect: this.reconnect,\n                reconnect_interval: this.reconnect_interval,\n                max_reconnects: this.max_reconnects,\n                ...this.rest_options,\n            })\n    }\n\n    /**\n   * Connects to a defined server if not connected already.\n   * @method\n   * @return {Undefined}\n   */\n    connect()\n    {\n        if (this.socket) return\n\n        this._connect(this.address, {\n            autoconnect: this.autoconnect,\n            reconnect: this.reconnect,\n            reconnect_interval: this.reconnect_interval,\n            max_reconnects: this.max_reconnects,\n            ...this.rest_options,\n        })\n    }\n\n    /**\n   * Calls a registered RPC method on server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object|Array} params - optional method parameters\n   * @param {Number} timeout - RPC reply timeout value\n   * @param {Object} ws_opts - options passed to ws\n   * @return {Promise}\n   */\n    call(\n        method: string,\n        params?: IWSRequestParams,\n        timeout?: number,\n        ws_opts?: Parameters<NodeWebSocketType[\"send\"]>[1]\n    )\n    {\n        if (!ws_opts && \"object\" === typeof timeout)\n        {\n            ws_opts = timeout\n            timeout = null\n        }\n\n        return new Promise((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const rpc_id = this.generate_request_id(method, params)\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params: params || undefined,\n                id: rpc_id,\n            }\n\n            this.socket.send(this.dataPack.encode(message), ws_opts, (error) =>\n            {\n                if (error) return reject(error)\n\n                this.queue[rpc_id] = { promise: [resolve, reject] }\n\n                if (timeout)\n                {\n                    this.queue[rpc_id].timeout = setTimeout(() =>\n                    {\n                        delete this.queue[rpc_id]\n                        reject(new Error(\"reply timeout\"))\n                    }, timeout)\n                }\n            })\n        })\n    }\n\n    /**\n   * Logins with the other side of the connection.\n   * @method\n   * @param {Object} params - Login credentials object\n   * @return {Promise}\n   */\n    async login(params: IWSRequestParams)\n    {\n        const resp = await this.call(\"rpc.login\", params)\n\n        if (!resp) throw new Error(\"authentication failed\")\n\n        return resp\n    }\n\n    /**\n   * Fetches a list of client's methods registered on server.\n   * @method\n   * @return {Array}\n   */\n    async listMethods()\n    {\n        return await this.call(\"__listMethods\")\n    }\n\n    /**\n   * Sends a JSON-RPC 2.0 notification to server.\n   * @method\n   * @param {String} method - RPC method name\n   * @param {Object} params - optional method parameters\n   * @return {Promise}\n   */\n    notify(method: string, params?: IWSRequestParams)\n    {\n        return new Promise<void>((resolve, reject) =>\n        {\n            if (!this.ready) return reject(new Error(\"socket not ready\"))\n\n            const message = {\n                jsonrpc: \"2.0\",\n                method: method,\n                params,\n            }\n\n            this.socket.send(this.dataPack.encode(message), (error) =>\n            {\n                if (error) return reject(error)\n\n                resolve()\n            })\n        })\n    }\n\n    /**\n   * Subscribes for a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async subscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.on\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\n                \"Failed subscribing to an event '\" + event + \"' with: \" + result[event]\n            )\n\n        return result\n    }\n\n    /**\n   * Unsubscribes from a defined event.\n   * @method\n   * @param {String|Array} event - event name\n   * @return {Undefined}\n   * @throws {Error}\n   */\n    async unsubscribe(event: string | Array<string>)\n    {\n        if (typeof event === \"string\") event = [event]\n\n        const result = await this.call(\"rpc.off\", event)\n\n        if (typeof event === \"string\" && result[event] !== \"ok\")\n            throw new Error(\"Failed unsubscribing from an event with: \" + result)\n\n        return result\n    }\n\n    /**\n   * Closes a WebSocket connection gracefully.\n   * @method\n   * @param {Number} code - socket close code\n   * @param {String} data - optional data to be sent before closing\n   * @return {Undefined}\n   */\n    close(code?: number, data?: string)\n    {\n        this.socket.close(code || 1000, data)\n    }\n\n    /**\n   * Enable / disable automatic reconnection.\n   * @method\n   * @param {Boolean} reconnect - enable / disable reconnection\n   * @return {Undefined}\n   */\n    setAutoReconnect(reconnect: boolean)\n    {\n        this.reconnect = reconnect\n    }\n\n    /**\n   * Set the interval between reconnection attempts.\n   * @method\n   * @param {Number} interval - reconnection interval in milliseconds\n   * @return {Undefined}\n   */\n    setReconnectInterval(interval: number)\n    {\n        this.reconnect_interval = interval\n    }\n\n    /**\n   * Set the maximum number of reconnection attempts.\n   * @method\n   * @param {Number} max_reconnects - maximum reconnection attempts\n   * @return {Undefined}\n   */\n    setMaxReconnects(max_reconnects: number)\n    {\n        this.max_reconnects = max_reconnects\n    }\n\n    /**\n   * Connection/Message handler.\n   * @method\n   * @private\n   * @param {String} address - WebSocket API address\n   * @param {Object} options - ws options object\n   * @return {Undefined}\n   */\n    private _connect(\n        address: string,\n        options: IWSClientAdditionalOptions & NodeWebSocket.ClientOptions\n    )\n    {\n        clearTimeout(this.reconnect_timer_id)\n        this.socket = this.webSocketFactory(address, options)\n\n        this.socket.addEventListener(\"open\", () =>\n        {\n            this.ready = true\n            this.emit(\"open\")\n            this.current_reconnects = 0\n        })\n\n        this.socket.addEventListener(\"message\", ({ data: message }) =>\n        {\n            if (message instanceof ArrayBuffer)\n                message = Buffer.from(message).toString()\n\n            try\n            {\n                message = this.dataPack.decode(message)\n            }\n            catch (error)\n            {\n                return\n            }\n\n            // check if any listeners are attached and forward event\n            if (message.notification && this.listeners(message.notification).length)\n            {\n                if (!Object.keys(message.params).length)\n                    return this.emit(message.notification)\n\n                const args = [message.notification]\n\n                if (message.params.constructor === Object) args.push(message.params)\n                // using for-loop instead of unshift/spread because performance is better\n                else\n                    for (let i = 0; i < message.params.length; i++)\n                        args.push(message.params[i])\n\n                // run as microtask so that pending queue messages are resolved first\n                // eslint-disable-next-line prefer-spread\n                return Promise.resolve().then(() =>\n                {\n                    // eslint-disable-next-line prefer-spread\n                    this.emit.apply(this, args)\n                })\n            }\n\n            if (!this.queue[message.id])\n            {\n                // general JSON RPC 2.0 events\n                if (message.method)\n                {\n                    // run as microtask so that pending queue messages are resolved first\n                    return Promise.resolve().then(() =>\n                    {\n                        this.emit(message.method, message?.params)\n                    })\n                }\n\n                return\n            }\n\n            // reject early since server's response is invalid\n            if (\"error\" in message === \"result\" in message)\n                this.queue[message.id].promise[1](\n                    new Error(\n                        \"Server response malformed. Response must include either \\\"result\\\"\" +\n              \" or \\\"error\\\", but not both.\"\n                    )\n                )\n\n            if (this.queue[message.id].timeout)\n                clearTimeout(this.queue[message.id].timeout)\n\n            if (message.error) this.queue[message.id].promise[1](message.error)\n            else this.queue[message.id].promise[0](message.result)\n\n            delete this.queue[message.id]\n        })\n\n        this.socket.addEventListener(\"error\", (error) => this.emit(\"error\", error))\n\n        this.socket.addEventListener(\"close\", ({ code, reason }) =>\n        {\n            if (this.ready)\n            // Delay close event until internal state is updated\n                setTimeout(() => this.emit(\"close\", code, reason), 0)\n\n            this.ready = false\n            this.socket = undefined\n\n            if (code === 1000) return\n\n            this.current_reconnects++\n\n            if (\n                this.reconnect &&\n        (this.max_reconnects > this.current_reconnects ||\n          this.max_reconnects === 0)\n            )\n                this.reconnect_timer_id = setTimeout(\n                    () => this._connect(address, options),\n                    this.reconnect_interval\n                )\n        })\n    }\n}\n", "\"use strict\"\n\nimport { WebSocket } from \"./lib/client/websocket.browser.js\"\nimport { CommonClient } from \"./lib/client.js\"\nimport { IWSClientAdditionalOptions } from \"./lib/client/client.types.js\"\n\nexport class Client extends CommonClient\n{\n    constructor(\n        address = \"ws://localhost:8080\",\n        {\n            autoconnect = true,\n            reconnect = true,\n            reconnect_interval = 1000,\n            max_reconnects = 5,\n        }: IWSClientAdditionalOptions = {},\n        generate_request_id?: (\n      method: string,\n      params: object | Array<any>\n    ) => number | string\n    )\n    {\n        super(\n            WebSocket,\n            address,\n            {\n                autoconnect,\n                reconnect,\n                reconnect_interval,\n                max_reconnects,\n            },\n            generate_request_id\n        )\n    }\n}\n\nexport * from \"./lib/client.js\"\nexport * from \"./lib/client/websocket.browser.js\"\nexport * from \"./lib/client/client.types.js\"\nexport * from \"./lib/utils.js\"\n"]}