# This file is generated by gyp; do not edit.

TOOLSET := target
TARGET := bigint_buffer
DEFS_Debug := \
	'-DNODE_GYP_MODULE_NAME=bigint_buffer' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D_LARGEFILE_SOURCE' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DBUILDING_NODE_EXTENSION' \
	'-DDEBUG' \
	'-D_DEBUG'

# Flags passed to all source files.
CFLAGS_Debug := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-g \
	-O0

# Flags passed to only C files.
CFLAGS_C_Debug :=

# Flags passed to only C++ files.
CFLAGS_CC_Debug := \
	-fno-rtti \
	-fno-exceptions \
	-fno-strict-aliasing \
	-std=gnu++20

INCS_Debug := \
	-I/home/<USER>/.cache/node-gyp/24.4.1/include/node \
	-I/home/<USER>/.cache/node-gyp/24.4.1/src \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/v8/include

DEFS_Release := \
	'-DNODE_GYP_MODULE_NAME=bigint_buffer' \
	'-DUSING_UV_SHARED=1' \
	'-DUSING_V8_SHARED=1' \
	'-DV8_DEPRECATION_WARNINGS=1' \
	'-D_GLIBCXX_USE_CXX11_ABI=1' \
	'-D_FILE_OFFSET_BITS=64' \
	'-D_LARGEFILE_SOURCE' \
	'-D__STDC_FORMAT_MACROS' \
	'-DOPENSSL_NO_PINSHARED' \
	'-DOPENSSL_THREADS' \
	'-DBUILDING_NODE_EXTENSION'

# Flags passed to all source files.
CFLAGS_Release := \
	-fPIC \
	-pthread \
	-Wall \
	-Wextra \
	-Wno-unused-parameter \
	-m64 \
	-O3 \
	-fno-omit-frame-pointer

# Flags passed to only C files.
CFLAGS_C_Release :=

# Flags passed to only C++ files.
CFLAGS_CC_Release := \
	-fno-rtti \
	-fno-exceptions \
	-fno-strict-aliasing \
	-std=gnu++20

INCS_Release := \
	-I/home/<USER>/.cache/node-gyp/24.4.1/include/node \
	-I/home/<USER>/.cache/node-gyp/24.4.1/src \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/openssl/config \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/openssl/openssl/include \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/uv/include \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/zlib \
	-I/home/<USER>/.cache/node-gyp/24.4.1/deps/v8/include

OBJS := \
	$(obj).target/$(TARGET)/src/bigint-buffer.o

# Add to the list of files we specially track dependencies for.
all_deps += $(OBJS)

# CFLAGS et al overrides must be target-local.
# See "Target-specific Variable Values" in the GNU Make manual.
$(OBJS): TOOLSET := $(TOOLSET)
$(OBJS): GYP_CFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_C_$(BUILDTYPE))
$(OBJS): GYP_CXXFLAGS := $(DEFS_$(BUILDTYPE)) $(INCS_$(BUILDTYPE))  $(CFLAGS_$(BUILDTYPE)) $(CFLAGS_CC_$(BUILDTYPE))

# Suffix rules, putting all outputs into $(obj).

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(srcdir)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# Try building from generated source, too.

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj).$(TOOLSET)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

$(obj).$(TOOLSET)/$(TARGET)/%.o: $(obj)/%.c FORCE_DO_CMD
	@$(call do_cmd,cc,1)

# End of this set of suffix rules
### Rules for final target.
LDFLAGS_Debug := \
	-pthread \
	-rdynamic \
	-m64

LDFLAGS_Release := \
	-pthread \
	-rdynamic \
	-m64

LIBS :=

$(obj).target/bigint_buffer.node: GYP_LDFLAGS := $(LDFLAGS_$(BUILDTYPE))
$(obj).target/bigint_buffer.node: LIBS := $(LIBS)
$(obj).target/bigint_buffer.node: TOOLSET := $(TOOLSET)
$(obj).target/bigint_buffer.node: $(OBJS) FORCE_DO_CMD
	$(call do_cmd,solink_module)

all_deps += $(obj).target/bigint_buffer.node
# Add target alias
.PHONY: bigint_buffer
bigint_buffer: $(builddir)/bigint_buffer.node

# Copy this to the executable output path.
$(builddir)/bigint_buffer.node: TOOLSET := $(TOOLSET)
$(builddir)/bigint_buffer.node: $(obj).target/bigint_buffer.node FORCE_DO_CMD
	$(call do_cmd,copy)

all_deps += $(builddir)/bigint_buffer.node
# Short alias for building this executable.
.PHONY: bigint_buffer.node
bigint_buffer.node: $(obj).target/bigint_buffer.node $(builddir)/bigint_buffer.node

# Add executable to "all" target.
.PHONY: all
all: $(builddir)/bigint_buffer.node

