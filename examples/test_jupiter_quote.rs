use solquant::jupiter::swap::{JupiterSwap, WSOL_MINT, USDC_MINT};
use tracing_subscriber;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt::init();

    println!("测试 Jupiter API 报价功能");
    println!("WSOL -> USDC");

    let jupiter = JupiterSwap::new();
    
    // 测试 0.1 SOL -> USDC 的报价
    match jupiter.get_quote(
        WSOL_MINT,
        USDC_MINT,
        100_000_000, // 0.1 SOL
        300,         // 3% 滑点
    ).await {
        Ok(quote) => {
            println!("✅ 报价成功!");
            println!("  输入: {} lamports ({} SOL)", quote.in_amount, quote.in_amount.parse::<u64>().unwrap_or(0) as f64 / 1e9);
            println!("  输出: {} micro-USDC ({} USDC)", quote.out_amount, quote.out_amount.parse::<u64>().unwrap_or(0) as f64 / 1e6);
            println!("  价格影响: {}%", quote.price_impact_pct);
            println!("  滑点: {} bps", quote.slippage_bps);
            println!("  路由计划: {} 个步骤", quote.route_plan.len());
            
            for (i, route) in quote.route_plan.iter().enumerate() {
                println!("    步骤 {}: {} ({}%)", i + 1, route.swap_info.label, route.percent);
            }
        }
        Err(e) => {
            println!("❌ 报价失败: {}", e);
        }
    }

    println!("\n测试 USDC -> WSOL");
    
    // 测试 10 USDC -> SOL 的报价
    match jupiter.get_quote(
        USDC_MINT,
        WSOL_MINT,
        10_000_000, // 10 USDC
        300,        // 3% 滑点
    ).await {
        Ok(quote) => {
            println!("✅ 报价成功!");
            println!("  输入: {} micro-USDC ({} USDC)", quote.in_amount, quote.in_amount.parse::<u64>().unwrap_or(0) as f64 / 1e6);
            println!("  输出: {} lamports ({} SOL)", quote.out_amount, quote.out_amount.parse::<u64>().unwrap_or(0) as f64 / 1e9);
            println!("  价格影响: {}%", quote.price_impact_pct);
            println!("  滑点: {} bps", quote.slippage_bps);
            println!("  路由计划: {} 个步骤", quote.route_plan.len());
            
            for (i, route) in quote.route_plan.iter().enumerate() {
                println!("    步骤 {}: {} ({}%)", i + 1, route.swap_info.label, route.percent);
            }
        }
        Err(e) => {
            println!("❌ 报价失败: {}", e);
        }
    }

    Ok(())
}
