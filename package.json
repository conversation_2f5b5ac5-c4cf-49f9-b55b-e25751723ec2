{"name": "solquant-ts", "version": "1.0.0", "description": "SOL to USDC swap using Raydium on Solana testnet", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "dev": "ts-node src/main.ts", "demo": "ts-node src/demo.ts", "test": "jest"}, "dependencies": {"@solana/web3.js": "^1.87.6", "@solana/spl-token": "^0.3.9", "@raydium-io/raydium-sdk": "^1.3.1-beta.58", "bn.js": "^5.2.1", "bs58": "^5.0.0", "decimal.js": "^10.4.3", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/bn.js": "^5.1.5", "typescript": "^5.3.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["solana", "raydium", "defi", "swap", "typescript"], "author": "Your Name", "license": "MIT"}