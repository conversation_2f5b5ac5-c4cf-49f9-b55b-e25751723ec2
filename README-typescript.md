# SolQuant TypeScript - SOL to USDC Swap

A simple TypeScript implementation for swapping <PERSON><PERSON> to USDC using Raydium on Solana devnet.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Setup Environment

Copy the example environment file and fill in your details:

```bash
cp .env.example .env
```

Edit `.env` file:
```env
# Solana RPC URL (testnet)
RPC_URL=https://api.devnet.solana.com

# Your wallet private key (base58 encoded)
PRIVATE_KEY=your_private_key_here

# Raydium API URL
RAYDIUM_API_URL=https://api-v3-devnet.raydium.io

# Slippage tolerance (in basis points, 50 = 0.5%)
SLIPPAGE_BPS=50

# Amount to swap (in SOL)
SWAP_AMOUNT=0.1
```

### 3. Get Devnet SOL

Get some devnet SOL from the faucet:
- Visit: https://faucet.solana.com/
- Enter your wallet address
- Request devnet SOL

### 4. Run the Swap

```bash
# Development mode (with TypeScript)
npm run dev

# Or build and run
npm run build
npm start
```

## 📁 Project Structure

```
src/
├── main.ts          # Main entry point
├── config.ts        # Configuration and environment variables
├── types.ts         # TypeScript type definitions
├── wallet.ts        # Solana wallet management
├── raydium.ts       # Raydium API client
└── swap.ts          # Swap logic and execution
```

## 🆚 TypeScript vs Rust Comparison

### TypeScript Advantages:
- ✅ **Faster Development**: Quicker to write and iterate
- ✅ **Rich Ecosystem**: Abundant DeFi libraries and tools
- ✅ **Easy Debugging**: Better debugging tools and error messages
- ✅ **JSON Handling**: Native JSON support, perfect for APIs
- ✅ **Community**: Large Solana/DeFi TypeScript community

### Rust Advantages:
- ✅ **Performance**: Faster execution and lower memory usage
- ✅ **Type Safety**: Stronger compile-time guarantees
- ✅ **Native Integration**: Direct integration with Solana programs
- ✅ **Production Ready**: Better for high-frequency trading systems

## 🔧 How to Test

1. **Install dependencies**: `npm install`
2. **Setup environment**: Copy `.env.example` to `.env` and fill in your private key
3. **Get devnet SOL**: Visit https://faucet.solana.com/
4. **Run the swap**: `npm run dev`

The tool will:
- Connect to Solana devnet
- Check your wallet balance
- Query Raydium for the best swap route
- Execute the SOL → USDC swap
- Show transaction results

## 🛡️ Security Notes

- **Never commit your private key to git**
- Use devnet for testing only
- Keep your private keys secure

## 📊 Expected Output

```
🚀 SolQuant TypeScript - SOL to USDC Swap on Raydium
✅ Configuration validated
✅ Raydium API connected
📊 Wallet Status: 1.500000 SOL
🔄 Starting swap: 0.1 SOL → USDC
📊 Expected output: ~9.845 USDC
✅ Transaction confirmed!
🎉 Swap completed successfully!
```
